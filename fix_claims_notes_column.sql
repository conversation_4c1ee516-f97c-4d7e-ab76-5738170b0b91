-- Fix Claims Table Schema - Add Missing 'notes' Column
-- This addresses the error: "Could not find the 'notes' column of 'claims' in the schema cache"

-- Add notes column to claims table
ALTER TABLE claims 
ADD COLUMN IF NOT EXISTS notes TEXT;

-- Add comment for documentation
COMMENT ON COLUMN claims.notes IS 'Additional notes and comments about the claim status updates';

-- Update RLS policies if needed (allow authenticated users to read/write notes)
-- This ensures the notes column is accessible through the API

-- Verify the column was added successfully
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'claims' AND column_name = 'notes';

-- Optional: Add an index on notes column for better search performance
CREATE INDEX IF NOT EXISTS idx_claims_notes ON claims USING gin(to_tsvector('english', notes)); 