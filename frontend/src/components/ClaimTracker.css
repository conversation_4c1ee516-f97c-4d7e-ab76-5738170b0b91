.claim-tracker {
  background-color: #ffffff;
  min-height: 100vh;
}

/* Hero Header Section - Blue background like in screenshot */
.hero-header {
  background: linear-gradient(135deg, #005AAF 0%, #0066CC 100%);
  color: white;
  padding: 4rem 0;
  text-align: center;
}

.hero-title {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 1rem;
  letter-spacing: -0.02em;
}

.hero-subtitle {
  font-size: 1.25rem;
  opacity: 0.95;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

/* Search Section */
.search-section {
  padding: 3rem 0;
  background-color: #f8f9fa;
}

.search-card {
  background: white;
  border-radius: 16px;
  padding: 2.5rem;
  box-shadow: 0 8px 25px rgba(0, 90, 175, 0.08);
  max-width: 700px;
  margin: 0 auto;
  border: 1px solid rgba(0, 90, 175, 0.1);
}

.search-title {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  font-size: 1.75rem;
  font-weight: 600;
  color: #343a40;
  margin-bottom: 2rem;
  text-align: center;
}

.search-icon {
  font-size: 1.5rem;
}

.search-form {
  text-align: center;
}

.search-input-group {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
  align-items: stretch;
}

.search-input {
  flex: 1;
  padding: 1rem 1.5rem;
  border: 2px solid #e9ecef;
  border-radius: 12px;
  font-size: 1rem;
  transition: all 0.3s ease;
  background-color: #ffffff;
}

.search-input:focus {
  outline: none;
  border-color: #005AAF;
  box-shadow: 0 0 0 3px rgba(0, 90, 175, 0.1);
}

.search-button {
  padding: 1rem 2rem;
  background: linear-gradient(135deg, #005AAF 0%, #0066CC 100%);
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 140px;
}

.search-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 90, 175, 0.3);
}

.search-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.search-help {
  color: #6c757d;
  font-size: 0.9rem;
  margin-top: 0.5rem;
  font-style: italic;
}

.error-message {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #dc3545;
  background-color: #f8d7da;
  border: 1px solid #f5c6cb;
  padding: 0.75rem 1rem;
  border-radius: 8px;
  margin-top: 1rem;
  font-size: 0.95rem;
}

.error-icon {
  font-size: 1.1rem;
}

/* Help Section - FAQ and Contact */
.help-section {
  background-color: #ffffff;
  padding: 4rem 0;
  border-top: 1px solid #e9ecef;
}

.help-title {
  font-size: 2.5rem;
  font-weight: 700;
  text-align: center;
  color: #343a40;
  margin-bottom: 3rem;
}

.help-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  max-width: 1000px;
  margin: 0 auto;
}

.help-column {
  background: #f8f9fa;
  padding: 2.5rem;
  border-radius: 16px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
}

.help-section-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #343a40;
  margin-bottom: 2rem;
  text-align: center;
}

.faq-list {
  space-y: 1rem;
}

.faq-item {
  padding: 1rem 0;
  color: #495057;
  font-size: 1rem;
  line-height: 1.6;
  border-bottom: 1px solid #e9ecef;
}

.faq-item:last-child {
  border-bottom: none;
}

.contact-list {
  space-y: 1.5rem;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: white;
  border-radius: 12px;
  font-size: 1rem;
  color: #343a40;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.contact-icon {
  font-size: 1.2rem;
  width: 2rem;
  text-align: center;
}

/* Timeline Progress Section - Improved Design */
.progress-timeline-card {
  background: white;
  border-radius: 16px;
  padding: 2.5rem;
  margin: 2rem 0;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
  border: 1px solid #e9ecef;
}

.timeline-title {
  font-size: 1.75rem;
  font-weight: 600;
  color: #343a40;
  margin-bottom: 2.5rem;
  text-align: center;
}

.timeline {
  position: relative;
  max-width: 800px;
  margin: 0 auto;
  padding: 0;
}

.timeline-item {
  display: flex;
  margin-bottom: 3rem;
  position: relative;
  align-items: flex-start;
}

.timeline-item:last-child {
  margin-bottom: 0;
}

.timeline-marker {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: 2rem;
  position: relative;
  z-index: 2;
}

.timeline-dot {
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 1rem;
  border: 3px solid #e9ecef;
  background: white;
  color: #6c757d;
  transition: all 0.3s ease;
  position: relative;
  z-index: 3;
}

/* Completed step styling */
.timeline-item.completed .timeline-dot {
  background: #005AAF;
  border-color: #005AAF;
  color: white;
  box-shadow: 0 4px 12px rgba(0, 90, 175, 0.3);
}

.timeline-item.completed .checkmark {
  font-size: 1.2rem;
  font-weight: bold;
}

/* Current step styling */
.timeline-item.current .timeline-dot {
  background: linear-gradient(135deg, #005AAF 0%, #0066CC 100%);
  border-color: #005AAF;
  color: white;
  box-shadow: 0 6px 20px rgba(0, 90, 175, 0.4);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 6px 20px rgba(0, 90, 175, 0.4);
  }
  50% {
    box-shadow: 0 8px 25px rgba(0, 90, 175, 0.6);
  }
  100% {
    box-shadow: 0 6px 20px rgba(0, 90, 175, 0.4);
  }
}

/* Connecting line between timeline items */
.timeline-line {
  width: 3px;
  height: 4rem;
  background: #e9ecef;
  margin-top: 0.5rem;
  border-radius: 2px;
  transition: all 0.3s ease;
}

.timeline-line.completed {
  background: linear-gradient(to bottom, #005AAF 0%, #0066CC 100%);
  box-shadow: 0 2px 8px rgba(0, 90, 175, 0.2);
}

/* Timeline content styling */
.timeline-content {
  flex: 1;
  padding-top: 0.5rem;
}

.timeline-step-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #343a40;
  margin-bottom: 0.5rem;
}

.timeline-item.completed .timeline-step-title {
  color: #005AAF;
}

.timeline-item.current .timeline-step-title {
  color: #005AAF;
  font-weight: 700;
}

.timeline-step-description {
  color: #6c757d;
  font-size: 1rem;
  line-height: 1.6;
  margin-bottom: 1rem;
}

.timeline-item.current .timeline-step-description {
  color: #495057;
  font-weight: 500;
}

/* Current step indicator */
.current-step-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #005AAF;
  font-size: 0.9rem;
  font-weight: 600;
  margin-top: 0.5rem;
}

.indicator-dot {
  width: 8px;
  height: 8px;
  background: #005AAF;
  border-radius: 50%;
  animation: blink 1.5s infinite;
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0.3; }
}

.indicator-text {
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Responsive timeline design */
@media (max-width: 768px) {
  .timeline-item {
    margin-bottom: 2.5rem;
  }
  
  .timeline-marker {
    margin-right: 1.5rem;
  }
  
  .timeline-dot {
    width: 2.5rem;
    height: 2.5rem;
    font-size: 0.9rem;
  }
  
  .timeline-line {
    height: 3rem;
  }
  
  .timeline-step-title {
    font-size: 1.1rem;
  }
  
  .timeline-step-description {
    font-size: 0.95rem;
  }
}

/* Step numbers for incomplete steps */
.step-number {
  font-size: 1rem;
  font-weight: 700;
}

/* Enhanced visual hierarchy */
.timeline-item.completed {
  opacity: 1;
}

.timeline-item:not(.completed):not(.current) {
  opacity: 0.7;
}

.timeline-item:not(.completed):not(.current) .timeline-step-title {
  color: #6c757d;
}

.timeline-item:not(.completed):not(.current) .timeline-step-description {
  color: #adb5bd;
}

/* Responsive Design */
@media (max-width: 768px) {
  .hero-title {
    font-size: 2rem;
  }
  
  .hero-subtitle {
    font-size: 1.1rem;
  }
  
  .search-input-group {
    flex-direction: column;
  }
  
  .search-button {
    min-width: auto;
  }
  
  .help-grid {
    grid-template-columns: 1fr;
    gap: 2rem;
  }
  
  .help-title {
    font-size: 2rem;
  }
} 