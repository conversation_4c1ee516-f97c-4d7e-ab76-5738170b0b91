# 🐳 Dockerfile for Zurich AI Claims Processing System
# Multi-stage build for optimized production image with frontend and backend

# Stage 1: Frontend build
FROM node:18-alpine as frontend-builder

WORKDIR /app/frontend

# Copy frontend package files
COPY frontend/package.json frontend/package-lock.json ./

# Install frontend dependencies (including dev dependencies needed for build)
RUN npm ci

# Copy frontend source code
COPY frontend/ ./

# Build frontend
RUN npm run build

# Stage 2: Python backend builder
FROM python:3.11-slim as backend-builder

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1

# Install system dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    curl \
    git \
    && rm -rf /var/lib/apt/lists/*

# Create and set working directory
WORKDIR /app

# Copy requirements first for better caching
COPY requirements.txt .

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Install BAML CLI
RUN pip install baml-py

# Stage 3: Backend-only stage (Clean logs for Dozzle)
FROM python:3.11-slim as backend-only

# Set environment variables for backend
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PYTHONPATH=/app/src

# Install runtime dependencies (no nginx needed for backend-only)
RUN apt-get update && apt-get install -y \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Create non-root user
RUN groupadd -r appuser && useradd -r -g appuser appuser

# Create app directory
WORKDIR /app

# Copy Python packages from backend builder
COPY --from=backend-builder /usr/local/lib/python3.11/site-packages /usr/local/lib/python3.11/site-packages
COPY --from=backend-builder /usr/local/bin /usr/local/bin

# Copy application code (backend only)
COPY src/ ./src/
COPY baml_src/ ./baml_src/
COPY main.py ./
COPY requirements.txt ./

# Generate BAML client
RUN baml-cli generate

# Create logs directory with proper permissions
RUN mkdir -p /app/logs && \
    chown -R appuser:appuser /app/logs

# Switch to non-root user
USER appuser

# Expose API port only
EXPOSE 8000

# Health check for backend API only
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# Run backend API server directly (cleaner logs)
CMD ["python", "main.py"]

# Stage 4: Frontend-only stage
FROM nginx:alpine as frontend-only

# Copy built frontend from frontend builder
COPY --from=frontend-builder /app/frontend/dist /usr/share/nginx/html

# Copy custom nginx config for React SPA
RUN echo 'server { \
    listen 80; \
    root /usr/share/nginx/html; \
    index index.html; \
    location / { \
        try_files $uri $uri/ /index.html; \
    } \
    location /api/ { \
        proxy_pass http://zurich-backend:8000/api/; \
        proxy_set_header Host $host; \
        proxy_set_header X-Real-IP $remote_addr; \
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for; \
        proxy_set_header X-Forwarded-Proto $scheme; \
    } \
}' > /etc/nginx/conf.d/default.conf

# Expose frontend port
EXPOSE 80

# Health check for frontend
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:80/ || exit 1

# Run nginx
CMD ["nginx", "-g", "daemon off;"]

# Stage 5: Production stage (Combined - kept for backward compatibility)
FROM python:3.11-slim as production

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PYTHONPATH=/app/src

# Install runtime dependencies including nginx for serving static files
RUN apt-get update && apt-get install -y \
    curl \
    nginx \
    supervisor \
    && rm -rf /var/lib/apt/lists/*

# Create non-root user
RUN groupadd -r appuser && useradd -r -g appuser appuser

# Create app directory
WORKDIR /app

# Copy Python packages from backend builder
COPY --from=backend-builder /usr/local/lib/python3.11/site-packages /usr/local/lib/python3.11/site-packages
COPY --from=backend-builder /usr/local/bin /usr/local/bin

# Copy application code (backend)
COPY . .

# Copy built frontend from frontend builder
COPY --from=frontend-builder /app/frontend/dist /app/frontend/dist

# Generate BAML client
RUN baml-cli generate

# Create logs, nginx, and temporary directories with proper permissions
RUN mkdir -p /app/logs /var/log/nginx /var/log/supervisor \
             /tmp/nginx_client_temp /tmp/nginx_proxy_temp \
             /tmp/nginx_fastcgi_temp /tmp/nginx_uwsgi_temp /tmp/nginx_scgi_temp \
             /var/cache/nginx && \
    chown -R appuser:appuser /app/logs && \
    chown -R appuser:appuser /var/log/nginx && \
    chown -R appuser:appuser /var/log/supervisor && \
    chown -R appuser:appuser /tmp/nginx_* && \
    chown -R appuser:appuser /var/cache/nginx && \
    chmod 755 /tmp/nginx_*

# Copy nginx configuration
COPY docker/nginx.conf /etc/nginx/nginx.conf

# Copy supervisor configuration
COPY docker/supervisord.conf /etc/supervisor/conf.d/supervisord.conf

# Switch to non-root user
USER appuser

# Expose ports (8000 for API, 80 for frontend)
EXPOSE 8000 80

# Health check for both services
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health && curl -f http://localhost:80/ || exit 1

# Run both services using supervisor
CMD ["/usr/bin/supervisord", "-c", "/etc/supervisor/conf.d/supervisord.conf"]
