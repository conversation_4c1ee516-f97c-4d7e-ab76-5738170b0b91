#!/usr/bin/env python3
"""
🧵 Email Threading Example - Zurich Claims Processing

This example demonstrates how to use the enhanced email service with proper
threading support for professional customer communication.

Features demonstrated:
- Parsing incoming emails to extract threading information
- Sending acknowledgment emails that stay in the same thread
- Proper email headers (In-Reply-To, References, Message-ID)
- Original message context preservation
- Professional email formatting with "Re:" prefixes
"""

import asyncio
import sys
import os
sys.path.append('src')

from src.config.settings import Settings
from src.communications.email_service import ProfessionalEmailService, EmailParser, EmailThreadInfo

# Example raw email content (what you'd receive from customer)
SAMPLE_CUSTOMER_EMAIL = """From: <EMAIL>
To: <EMAIL>
Subject: Slip and Fall Claim Submission - John Doe
Date: Fri, 29 Jun 2025 08:30:00 -0400
Message-ID: <<EMAIL>>

Dear Zurich Insurance,

I am writing to report a slip and fall incident that occurred at the No Frills 
grocery store on August 27, 2020. I was shopping when I slipped on a wet floor 
that had no warning signs posted. 

I sustained injuries to my back and knee, and have medical documentation from 
my doctor. I am attaching the following documents:
- Medical reports from Dr<PERSON> <PERSON>
- <PERSON>s of the incident location
- Receipt showing I was a customer at the time

Please let me know what additional information you need to process my claim.

Best regards,
John Doe
Phone: (*************
Email: <EMAIL>
"""

async def demonstrate_email_threading():
    """Demonstrate complete email threading workflow"""
    
    print("🧵 Email Threading Demonstration")
    print("=" * 60)
    
    # Initialize email service
    try:
        settings = Settings()
        email_service = ProfessionalEmailService(settings)
        print("✅ Email service initialized successfully")
    except Exception as e:
        print(f"❌ Failed to initialize email service: {e}")
        print("💡 Make sure your environment variables are set properly")
        return
    
    print("\n📧 Step 1: Parse incoming customer email")
    print("-" * 40)
    
    # Parse the incoming customer email
    thread_info = email_service.extract_thread_info_from_raw_email(SAMPLE_CUSTOMER_EMAIL)
    
    if thread_info:
        print(f"✅ Successfully parsed email threading information:")
        print(f"   📎 Message-ID: {thread_info.message_id}")
        print(f"   📝 Subject: {thread_info.original_subject}")
        print(f"   👤 Sender: {thread_info.original_sender}")
        print(f"   📅 Date: {thread_info.original_date}")
        print(f"   💬 Preview: {thread_info.original_body_preview[:100]}...")
    else:
        print("❌ Failed to parse email threading information")
        return
    
    print("\n📧 Step 2: Generate claim reference and tracking URL")
    print("-" * 40)
    
    # Generate claim details
    claim_id = "PI20250629001"  # Personal Injury claim
    customer_name = "John Doe"
    customer_email = "<EMAIL>"
    claim_type = "Personal Injury Claim"
    incident_date = "2020-08-27"
    tracking_url = email_service.generate_tracking_url(claim_id)
    
    print(f"🔖 Claim ID: {claim_id}")
    print(f"🔗 Tracking URL: {tracking_url}")
    
    print("\n📧 Step 3: Send threaded acknowledgment email")
    print("-" * 40)
    
    # Send acknowledgment as a reply in the same thread
    success = await email_service.send_claim_acknowledgment(
        claim_id=claim_id,
        customer_email=customer_email,
        customer_name=customer_name,
        claim_type=claim_type,
        incident_date=incident_date,
        tracking_url=tracking_url,
        thread_info=thread_info  # This makes it a threaded reply!
    )
    
    if success:
        print("✅ Threaded acknowledgment email sent successfully!")
        print("   📧 Email will appear as a reply in the customer's email client")
        print("   🔗 Customer can track their claim using the provided link")
        print("   💬 Original message context is preserved in the email")
    else:
        print("❌ Failed to send threaded acknowledgment email")
    
    print("\n📧 Step 4: Demonstrate manual threading")
    print("-" * 40)
    
    # Example of sending a custom threaded reply
    custom_subject = "Additional Documentation Required"
    custom_html = """
    <html>
    <body style="font-family: Arial, sans-serif;">
        <h3 style="color: #005AAF;">Additional Documentation Required</h3>
        <p>Dear John,</p>
        <p>Thank you for your claim submission. To proceed with your claim processing, 
        we need the following additional documents:</p>
        <ul>
            <li>Police report (if available)</li>
            <li>Witness statements</li>
            <li>Store incident report</li>
        </ul>
        <p>Please reply to this email with the requested documents.</p>
        <p>Best regards,<br>Zurich Claims Team</p>
    </body>
    </html>
    """
    
    custom_text = """
Additional Documentation Required

Dear John,

Thank you for your claim submission. To proceed with your claim processing, 
we need the following additional documents:

- Police report (if available)
- Witness statements  
- Store incident report

Please reply to this email with the requested documents.

Best regards,
Zurich Claims Team
    """
    
    # Send custom threaded reply
    custom_success = await email_service.send_threaded_reply(
        original_email_content=SAMPLE_CUSTOMER_EMAIL,
        reply_subject=custom_subject,
        reply_html_body=custom_html,
        reply_text_body=custom_text,
        to_email=customer_email
    )
    
    if custom_success:
        print("✅ Custom threaded reply sent successfully!")
        print("   📧 Email maintains thread continuity")
        print("   💼 Professional formatting with business requirements")
    else:
        print("❌ Failed to send custom threaded reply")
    
    print("\n📧 Step 5: Email threading best practices summary")
    print("-" * 40)
    
    print("✅ Email Threading Best Practices Implemented:")
    print("   🔗 Proper In-Reply-To and References headers")
    print("   📝 Subject line maintains thread with 'Re:' prefix")
    print("   💬 Original message context preserved")
    print("   🆔 Unique Message-ID for each outgoing email")
    print("   🎯 Thread continuity in email clients")
    print("   📋 Professional business communication standards")

def demonstrate_parsing_only():
    """Demonstrate email parsing without sending emails"""
    
    print("\n🔍 Email Parsing Demo (No Sending)")
    print("=" * 40)
    
    # Parse the sample email
    msg = EmailParser.parse_raw_email(SAMPLE_CUSTOMER_EMAIL)
    thread_info = EmailParser.extract_thread_info(msg)
    
    print("📧 Parsed Email Information:")
    print(f"   Message-ID: {thread_info.message_id}")
    print(f"   Subject: {thread_info.original_subject}")
    print(f"   From: {thread_info.original_sender}")
    print(f"   Date: {thread_info.original_date}")
    print(f"   Body Preview: {thread_info.original_body_preview}")
    
    # Show how subject would be formatted for reply
    reply_subject = "Claim Confirmation - Reference #PI20250629001 - Zurich Insurance"
    if thread_info.original_subject:
        clean_subject = thread_info.original_subject
        reply_subject = f"Re: {clean_subject}"
    
    print(f"\n📝 Reply Subject: {reply_subject}")
    
    # Show threading headers that would be added
    print("\n📬 Threading Headers that would be added:")
    print(f"   In-Reply-To: <{thread_info.message_id}>")
    print(f"   References: <{thread_info.message_id}>")
    print(f"   Message-ID: <<EMAIL>>")

async def main():
    """Main demonstration function"""
    
    print("🎯 Zurich Claims - Email Threading Demo")
    print("🔧 Professional email threading for customer communication")
    print("")
    
    # Check if we have email configuration
    try:
        settings = Settings()
        if not settings.claims_email or not settings.claims_email_password:
            print("⚠️  Email credentials not configured - running parsing demo only")
            demonstrate_parsing_only()
            return
    except:
        print("⚠️  Settings not available - running parsing demo only")
        demonstrate_parsing_only()
        return
    
    # Run full demonstration
    await demonstrate_email_threading()
    
    print("\n" + "=" * 60)
    print("🎉 Email threading demonstration complete!")
    print("\n📋 Integration Instructions:")
    print("1. Extract thread info from incoming emails using EmailParser")
    print("2. Pass thread_info to send_claim_acknowledgment() method")
    print("3. Use send_threaded_reply() for follow-up communications")
    print("4. All emails will maintain proper threading automatically")
    print("\n💡 Benefits:")
    print("✅ Customers see replies in same email thread")
    print("✅ Context preserved with original message quotes")
    print("✅ Professional email client compatibility")
    print("✅ Improved customer experience and clarity")

if __name__ == "__main__":
    asyncio.run(main()) 