"""
Dozzle-compatible logging utility for Docker log viewing.
Provides structured logging that's optimized for Dozzle Docker log viewer.
"""

import logging
from datetime import datetime, timezone


def dozzle_log(level: str, message: str, **kwargs) -> None:
    """
    Log a message in Dozzle-compatible format.
    
    Args:
        level: Log level (info, warning, error, debug)
        message: Main log message
        **kwargs: Additional structured data to include in the log
    """
    # Create timestamp in ISO format
    timestamp = datetime.now(timezone.utc).strftime("%Y-%m-%d %H:%M:%S")

    # Normalize log level
    level = level.upper()
    
    # Format for Dozzle (JSON + readable format)
    readable_message = f"[{timestamp}] {level}: {message}"
    
    # Add structured data if present
    if kwargs:
        structured_data = " | ".join([f"{k}={v}" for k, v in kwargs.items()])
        readable_message += f" | {structured_data}"
    
    # Print readable format for Dozzle compatibility
    print(readable_message)  # For Dozzle readability


def setup_dozzle_logging():
    """Setup logging configuration for Dozzle compatibility."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler()
        ]
    )
