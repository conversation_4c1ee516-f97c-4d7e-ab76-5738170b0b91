"""
Enhanced Email Threading Utility
Based on 2025 research findings - combines the best available libraries:
- mail-parser-reply: Multi-language reply parsing 
- email.utils: Built-in Message-ID and threading header management
- Custom threading logic for conversation tracking
"""

import re
import uuid
from datetime import datetime
from typing import Dict, Any, Optional, List, Tuple, Set
from email.utils import make_msgid, parseaddr, formataddr
import structlog

logger = structlog.get_logger(__name__)

# Enhanced email parsing libraries (2025 research-based)
try:
    from mailparser_reply import EmailReplyParser
    ENHANCED_REPLY_PARSING = True
    logger.info("✅ Enhanced email reply parsing available (mail-parser-reply)")
except ImportError:
    ENHANCED_REPLY_PARSING = False
    logger.warning("⚠️ Enhanced email reply parsing not available - using basic parsing")


class EmailThreadingManager:
    """
    Advanced email threading manager combining:
    1. Enhanced reply content extraction (mail-parser-reply)
    2. Professional Message-ID management (email.utils)
    3. Conversation tracking and organization
    """
    
    def __init__(self, domain: str = "zurich.com"):
        self.domain = domain
        self.conversation_threads: Dict[str, List[Dict]] = {}
        
        # Multi-language support for international claims
        self.parser_languages = ['en', 'de', 'fr', 'es', 'it', 'nl', 'pl', 'sv']
        
        if ENHANCED_REPLY_PARSING:
            self.reply_parser = EmailReplyParser(
                languages=self.parser_languages,
                include_english=True  # Always include English as fallback
            )
    
    def generate_message_id(self, claim_id: Optional[str] = None) -> str:
        """
        Generate RFC-compliant Message-ID with optional claim reference
        Uses Python's built-in email.utils for proper formatting
        """
        if claim_id:
            # Create structured ID for claim-related emails
            unique_part = f"claim-{claim_id}-{uuid.uuid4().hex[:8]}"
        else:
            # Generate general unique ID
            unique_part = f"msg-{uuid.uuid4().hex[:12]}"
        
        # Use email.utils.make_msgid for RFC compliance
        message_id = make_msgid(idstring=unique_part, domain=self.domain)
        
        logger.info(f"Generated Message-ID: {message_id}", claim_id=claim_id)
        return message_id
    
    def extract_clean_reply(self, email_body: str) -> Dict[str, Any]:
        """
        Extract clean reply content using enhanced parsing
        Returns structured information about the email content
        """
        result = {
            'clean_content': email_body,
            'has_previous_messages': False,
            'reply_chain_length': 1,
            'parsing_method': 'basic'
        }
        
        if not email_body or not email_body.strip():
            return result
        
        if ENHANCED_REPLY_PARSING:
            try:
                # Use advanced multi-language parser
                email_message = self.reply_parser.read(text=email_body)
                
                if email_message.replies:
                    # Get the most recent reply (first in list)
                    latest_reply = email_message.replies[0]
                    result.update({
                        'clean_content': latest_reply.body or latest_reply.content,
                        'has_previous_messages': len(email_message.replies) > 1,
                        'reply_chain_length': len(email_message.replies),
                        'parsing_method': 'enhanced_multilang',
                        'signatures': getattr(latest_reply, 'signatures', []),
                        'headers': getattr(latest_reply, 'headers', []),
                        'disclaimers': getattr(latest_reply, 'disclaimers', [])
                    })
                    
                    logger.info(
                        f"Enhanced parsing extracted {len(email_message.replies)} replies",
                        method='mail-parser-reply',
                        languages=self.parser_languages
                    )
                else:
                    # No threading detected, use original content
                    result['clean_content'] = email_body
                    result['parsing_method'] = 'enhanced_single'
                    
            except Exception as e:
                logger.warning(f"Enhanced parsing failed: {e}, falling back to basic")
                result['parsing_method'] = 'basic_fallback'
        
        # Basic cleanup if enhanced parsing not available or failed
        if result['parsing_method'] in ['basic', 'basic_fallback']:
            result['clean_content'] = self._basic_reply_cleanup(email_body)
        
        return result
    
    def _basic_reply_cleanup(self, content: str) -> str:
        """Basic reply content cleanup for fallback"""
        if not content:
            return ""
        
        # Remove common reply indicators
        patterns = [
            r'^On .+wrote:.*$',  # "On [date] [person] wrote:"
            r'^From: .+$',       # Email headers
            r'^Sent: .+$',
            r'^To: .+$',
            r'^Subject: .+$',
            r'^>.*$',            # Quoted lines
            r'^\s*-----.*-----\s*$',  # Separators
        ]
        
        lines = content.split('\n')
        clean_lines = []
        
        for line in lines:
            # Skip lines matching reply patterns
            if any(re.match(pattern, line.strip(), re.IGNORECASE | re.MULTILINE) 
                   for pattern in patterns):
                continue
            clean_lines.append(line)
        
        return '\n'.join(clean_lines).strip()
    
    def create_thread_headers(self, 
                            message_id: str,
                            in_reply_to: Optional[str] = None,
                            references: Optional[List[str]] = None) -> Dict[str, str]:
        """
        Create proper email threading headers using email.utils
        """
        headers = {
            'Message-ID': message_id
        }
        
        if in_reply_to:
            headers['In-Reply-To'] = in_reply_to
            
        if references:
            # Build References header with complete thread history
            if in_reply_to and in_reply_to not in references:
                references.append(in_reply_to)
            headers['References'] = ' '.join(references)
        elif in_reply_to:
            # If no references but we have in-reply-to, use it as references
            headers['References'] = in_reply_to
            
        logger.info(
            "Created threading headers",
            message_id=message_id,
            in_reply_to=in_reply_to,
            references_count=len(references) if references else 0
        )
        
        return headers
    
    def track_conversation(self, 
                          message_id: str,
                          thread_id: str,
                          email_data: Dict[str, Any]) -> None:
        """Track email in conversation thread"""
        if thread_id not in self.conversation_threads:
            self.conversation_threads[thread_id] = []
            
        thread_entry = {
            'message_id': message_id,
            'timestamp': datetime.utcnow().isoformat(),
            'subject': email_data.get('subject', ''),
            'from': email_data.get('from', ''),
            'clean_content_preview': email_data.get('clean_content', '')[:200],
            'has_attachments': bool(email_data.get('attachments')),
            'parsing_method': email_data.get('parsing_method', 'basic')
        }
        
        self.conversation_threads[thread_id].append(thread_entry)
        
        logger.info(
            "Tracked conversation entry",
            thread_id=thread_id,
            message_id=message_id,
            thread_length=len(self.conversation_threads[thread_id])
        )
    
    def get_conversation_summary(self, thread_id: str) -> Dict[str, Any]:
        """Get summary of conversation thread"""
        if thread_id not in self.conversation_threads:
            return {'exists': False}
            
        thread = self.conversation_threads[thread_id]
        
        return {
            'exists': True,
            'message_count': len(thread),
            'participants': list(set(entry['from'] for entry in thread)),
            'first_message': thread[0]['timestamp'] if thread else None,
            'last_message': thread[-1]['timestamp'] if thread else None,
            'subjects': list(set(entry['subject'] for entry in thread if entry['subject'])),
            'has_attachments': any(entry['has_attachments'] for entry in thread),
            'parsing_methods_used': list(set(entry['parsing_method'] for entry in thread))
        }
    
    @staticmethod
    def extract_email_components(email_address: str) -> Tuple[str, str]:
        """
        Extract name and email from address using email.utils
        """
        name, addr = parseaddr(email_address)
        return name or "", addr or ""
    
    @staticmethod
    def format_email_address(name: str, email: str) -> str:
        """
        Format email address using email.utils
        """
        return formataddr((name, email))
    
    def generate_claim_thread_id(self, claim_id: str) -> str:
        """Generate consistent thread ID for claim conversations"""
        return f"claim-thread-{claim_id}"
    
    def get_threading_stats(self) -> Dict[str, Any]:
        """Get statistics about threading performance"""
        total_threads = len(self.conversation_threads)
        total_messages = sum(len(thread) for thread in self.conversation_threads.values())
        
        parsing_methods = {}
        for thread in self.conversation_threads.values():
            for entry in thread:
                method = entry['parsing_method']
                parsing_methods[method] = parsing_methods.get(method, 0) + 1
        
        return {
            'total_conversation_threads': total_threads,
            'total_tracked_messages': total_messages,
            'average_thread_length': total_messages / total_threads if total_threads > 0 else 0,
            'parsing_method_distribution': parsing_methods,
            'enhanced_parsing_available': ENHANCED_REPLY_PARSING,
            'supported_languages': self.parser_languages if ENHANCED_REPLY_PARSING else ['en']
        }


# Global instance for easy access
threading_manager = EmailThreadingManager() 