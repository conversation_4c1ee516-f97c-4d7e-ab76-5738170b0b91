"""
Email Monitor Service
Monitors the claims inbox for new emails and triggers the workflow
"""

import asyncio
import email
import email.message
import imaplib
import logging
import os
from datetime import datetime
from email.header import decode_header
from typing import Dict, List, Any, Callable
import base64

logger = logging.getLogger(__name__)

class EmailMonitor:
    """Monitors email inbox for new claims"""
    
    def __init__(self, config: Dict[str, Any], workflow_coordinator: Any = None):
        self.config = config
        self.workflow_coordinator = workflow_coordinator
        
        # 🚨 SECURITY: Use ONLY environment variables - NO hardcoded fallbacks
        self.email_address = config.get('claims_email')
        self.password = config.get('claims_email_password')
        self.imap_server = config.get('imap_server', 'imap.gmail.com')
        self.imap_port = config.get('imap_port', 993)
        
        # Validate required credentials
        if not self.email_address:
            raise ValueError("EMAIL environment variable is required")
        if not self.password:
            raise ValueError("CLAIMS_EMAIL_PASSWORD environment variable is required")
        self.is_monitoring = False
        self.processed_emails = set()
        
        logger.info(f"Email monitor initialized for {self.email_address}")
    
    async def start_monitoring(self, callback: Callable[[Dict[str, Any]], None] = None):
        """Start monitoring the email inbox"""
        try:
            self.is_monitoring = True
            self.callback = callback or self._default_callback
            
            logger.info("Starting email monitoring...")
            
            # Start monitoring loop
            asyncio.create_task(self._monitor_loop())
            
            logger.info("Email monitoring started successfully")
            
        except Exception as e:
            logger.error(f"Failed to start email monitoring: {e}")
            raise
    
    async def stop_monitoring(self):
        """Stop monitoring the email inbox"""
        self.is_monitoring = False
        logger.info("Email monitoring stopped")
    
    async def _monitor_loop(self):
        """Main monitoring loop"""
        while self.is_monitoring:
            try:
                await self._check_new_emails()
                await asyncio.sleep(30)  # Check every 30 seconds
                
            except Exception as e:
                logger.error(f"Error in email monitoring loop: {e}")
                await asyncio.sleep(60)  # Wait longer on error
    
    async def _check_new_emails(self):
        """Check for new emails in the inbox"""
        try:
            # Connect to IMAP server
            mail = imaplib.IMAP4_SSL(self.imap_server, self.imap_port)
            mail.login(self.email_address, self.password)
            mail.select('INBOX')
            
            # Search for unread emails
            status, messages = mail.search(None, 'UNSEEN')
            
            if status == 'OK' and messages[0]:
                email_ids = messages[0].split()
                
                for email_id in email_ids:
                    await self._process_email(mail, email_id)
            
            mail.close()
            mail.logout()
            
        except Exception as e:
            logger.error(f"Error checking emails: {e}")
    
    async def _process_email(self, mail: imaplib.IMAP4_SSL, email_id: bytes):
        """Process a single email"""
        try:
            # Fetch email data
            status, msg_data = mail.fetch(email_id, '(RFC822)')
            
            if status == 'OK':
                email_body = msg_data[0][1]
                email_message = email.message_from_bytes(email_body)
                
                # Extract email information
                email_data = await self._extract_email_data(email_message)
                
                # Check if we've already processed this email
                email_hash = self._generate_email_hash(email_data)
                if email_hash in self.processed_emails:
                    return
                
                # Mark as processed
                self.processed_emails.add(email_hash)
                
                # Call the callback to process the email
                await self.callback(email_data)
                
                # Mark email as read
                mail.store(email_id, '+FLAGS', '\\Seen')
                
                logger.info(f"Processed email: {email_data.get('subject', 'No subject')}")
            
        except Exception as e:
            logger.error(f"Error processing email {email_id}: {e}")
    
    async def _extract_email_data(self, email_message: email.message.Message) -> Dict[str, Any]:
        """Extract relevant data from email message"""
        try:
            # Extract headers
            subject = self._decode_header(email_message.get('Subject', ''))
            from_email = self._decode_header(email_message.get('From', ''))
            to_email = self._decode_header(email_message.get('To', ''))
            date = email_message.get('Date', '')
            message_id = email_message.get('Message-ID', '')
            
            # Extract sender name and email
            from_name, from_email_clean = self._parse_email_address(from_email)
            
            # Extract body
            body = await self._extract_body(email_message)
            
            # Extract attachments
            attachments = await self._extract_attachments(email_message)
            
            # Convert to workflow coordinator format (compatible with existing workflow)
            email_data = {
                # HumanLayer compatible format for existing workflow
                'from_address': from_email_clean,
                'to_address': to_email,
                'subject': subject,
                'body': body,  # This will be CLEAN, not corrupted!
                'message_id': message_id,
                'raw_email': email_message.as_string(),  # Full raw email
                'previous_thread': [],  # Can be enhanced later
                
                # Additional fields for enhanced processing
                'from_name': from_name,
                'sender_email': from_email_clean,
                'sender_name': from_name,
                'received_at': datetime.now().isoformat() + 'Z',
                'date': date,
                
                # Attachment information
                'attachments': attachments,
                'has_attachments': len(attachments) > 0,
                'attachments_mentioned': self._check_attachments_mentioned(body),
                'attachment_count': len(attachments),
                
                # Processing metadata
                'processed_at': datetime.now().isoformat() + 'Z',
                'source': 'direct_imap',
                'corruption_detected': False,  # IMAP emails won't be corrupted!
                'webhook_metadata': {
                    'event_type': 'direct_email.received',
                    'is_test': False,
                    'source': 'imap_monitor',
                    'body_parsing': {
                        'source': 'direct_imap',
                        'original_length': len(body),
                        'final_length': len(body),
                        'improvement_ratio': 1.0  # No corruption, no improvement needed
                    }
                }
            }
            
            return email_data
            
        except Exception as e:
            logger.error(f"Error extracting email data: {e}")
            return {}
    
    def _decode_header(self, header: str) -> str:
        """Decode email header"""
        try:
            if header:
                decoded_parts = decode_header(header)
                decoded_string = ""
                for part, encoding in decoded_parts:
                    if isinstance(part, bytes):
                        if encoding:
                            decoded_string += part.decode(encoding)
                        else:
                            decoded_string += part.decode('utf-8', errors='ignore')
                    else:
                        decoded_string += part
                return decoded_string
            return ""
        except Exception as e:
            logger.error(f"Error decoding header: {e}")
            return str(header) if header else ""
    
    def _parse_email_address(self, email_string: str) -> tuple:
        """Parse email address to extract name and email"""
        try:
            if '<' in email_string and '>' in email_string:
                # <AUTHOR> <EMAIL>"
                name_part = email_string.split('<')[0].strip().strip('"')
                email_part = email_string.split('<')[1].split('>')[0].strip()
                return name_part, email_part
            else:
                # Format: "<EMAIL>"
                return "", email_string.strip()
        except Exception as e:
            logger.error(f"Error parsing email address: {e}")
            return "", email_string
    
    async def _extract_body(self, email_message: email.message.Message) -> str:
        """Extract email body"""
        try:
            body = ""
            
            if email_message.is_multipart():
                for part in email_message.walk():
                    content_type = part.get_content_type()
                    content_disposition = str(part.get('Content-Disposition', ''))
                    
                    # Skip attachments
                    if 'attachment' in content_disposition:
                        continue
                    
                    # Get text content
                    if content_type == 'text/plain':
                        try:
                            payload = part.get_payload(decode=True)
                            charset = part.get_content_charset() or 'utf-8'
                            body += payload.decode(charset, errors='ignore')
                        except Exception as e:
                            logger.error(f"Error decoding text part: {e}")
                    
                    elif content_type == 'text/html':
                        # Convert HTML to plain text (basic conversion)
                        try:
                            payload = part.get_payload(decode=True)
                            charset = part.get_content_charset() or 'utf-8'
                            html_content = payload.decode(charset, errors='ignore')
                            # Basic HTML to text conversion
                            import re
                            text_content = re.sub(r'<[^>]+>', '', html_content)
                            text_content = re.sub(r'\s+', ' ', text_content).strip()
                            body += text_content
                        except Exception as e:
                            logger.error(f"Error decoding HTML part: {e}")
            else:
                # Not multipart
                payload = email_message.get_payload(decode=True)
                charset = email_message.get_content_charset() or 'utf-8'
                body = payload.decode(charset, errors='ignore')
            
            return body.strip()
            
        except Exception as e:
            logger.error(f"Error extracting email body: {e}")
            return ""
    
    async def _extract_attachments(self, email_message: email.message.Message) -> List[Dict[str, Any]]:
        """Extract email attachments"""
        try:
            logger.info(f"DEBUG: Starting attachment extraction")
            attachments = []

            if email_message.is_multipart():
                logger.info(f"DEBUG: Email is multipart, walking through parts")
                part_count = 0
                for part in email_message.walk():
                    part_count += 1
                    content_disposition = str(part.get('Content-Disposition', ''))
                    content_type = part.get_content_type()
                    logger.info(f"DEBUG: Part {part_count}: content_type={content_type}, disposition={content_disposition}")

                    if 'attachment' in content_disposition:
                        logger.info(f"DEBUG: Found attachment in part {part_count}")
                        filename = part.get_filename()
                        logger.info(f"DEBUG: Attachment filename: {filename}")

                        if filename:
                            filename = self._decode_header(filename)
                            logger.info(f"DEBUG: Decoded filename: {filename}")

                            # Get attachment data
                            payload = part.get_payload(decode=True)
                            logger.info(f"DEBUG: Attachment payload size: {len(payload) if payload else 0} bytes")

                            if payload:
                                # Save attachment temporarily
                                logger.info(f"DEBUG: Saving attachment to temporary file")
                                temp_path = await self._save_attachment(filename, payload)
                                logger.info(f"DEBUG: Attachment saved to: {temp_path}")

                                attachment_info = {
                                    'filename': filename,
                                    'content_type': part.get_content_type(),
                                    'size': len(payload),
                                    'temp_path': temp_path
                                }
                                attachments.append(attachment_info)
                                logger.info(f"DEBUG: Added attachment to list: {attachment_info}")
                            else:
                                logger.warning(f"DEBUG: No payload found for attachment {filename}")
                        else:
                            logger.warning(f"DEBUG: No filename found for attachment in part {part_count}")
                    else:
                        logger.info(f"DEBUG: Part {part_count} is not an attachment")

                logger.info(f"DEBUG: Processed {part_count} parts, found {len(attachments)} attachments")
            else:
                logger.info(f"DEBUG: Email is not multipart, no attachments expected")

            logger.info(f"DEBUG: Attachment extraction completed. Total attachments: {len(attachments)}")
            return attachments

        except Exception as e:
            logger.error(f"DEBUG: Error extracting attachments: {e}")
            import traceback
            logger.error(f"DEBUG: Traceback: {traceback.format_exc()}")
            return []
    
    async def _save_attachment(self, filename: str, payload: bytes) -> str:
        """Save attachment to temporary file"""
        try:
            logger.info(f"DEBUG: Saving attachment '{filename}' with {len(payload)} bytes")
            import tempfile
            import os

            # Create temp directory if it doesn't exist
            temp_dir = os.path.join(os.getcwd(), 'temp_attachments')
            logger.info(f"DEBUG: Temp directory: {temp_dir}")
            os.makedirs(temp_dir, exist_ok=True)
            logger.info(f"DEBUG: Temp directory created/exists")

            # Create unique filename
            import uuid
            unique_id = str(uuid.uuid4())[:8]
            safe_filename = "".join(c for c in filename if c.isalnum() or c in ('.-_')).rstrip()
            temp_filename = f"{unique_id}_{safe_filename}"
            temp_path = os.path.join(temp_dir, temp_filename)
            logger.info(f"DEBUG: Temp file path: {temp_path}")

            # Save file
            logger.info(f"DEBUG: Writing {len(payload)} bytes to file")
            with open(temp_path, 'wb') as f:
                f.write(payload)

            # Verify file was saved
            if os.path.exists(temp_path):
                file_size = os.path.getsize(temp_path)
                logger.info(f"DEBUG: File saved successfully. Size: {file_size} bytes")
            else:
                logger.error(f"DEBUG: File was not saved at {temp_path}")
                return ""

            return temp_path

        except Exception as e:
            logger.error(f"DEBUG: Error saving attachment: {e}")
            import traceback
            logger.error(f"DEBUG: Traceback: {traceback.format_exc()}")
            return ""
    
    def _check_attachments_mentioned(self, body: str) -> bool:
        """Check if attachments are mentioned in email body"""
        if not body:
            return False
            
        attachment_keywords = [
            "attached", "attachment", "please find attached", "enclosed", 
            "document", "file", "report", "pdf", "image", "scan"
        ]
        return any(keyword in body.lower() for keyword in attachment_keywords)
    
    def _generate_email_hash(self, email_data: Dict[str, Any]) -> str:
        """Generate a hash for the email to avoid duplicates"""
        try:
            import hashlib
            
            # Create hash from key email fields
            hash_string = f"{email_data.get('message_id', '')}{email_data.get('from_address', '')}{email_data.get('subject', '')}{email_data.get('date', '')}"
            return hashlib.md5(hash_string.encode()).hexdigest()
            
        except Exception as e:
            logger.error(f"Error generating email hash: {e}")
            return ""
    
    async def _default_callback(self, email_data: Dict[str, Any]):
        """Default callback that forwards to workflow coordinator"""
        if self.workflow_coordinator:
            # Convert email data to webhook format expected by workflow coordinator
            webhook_data = {
                "type": "agent_email.received",
                "event": email_data,
                "is_test": False
            }
            
            logger.info(f"🔄 [EMAIL_MONITOR] Forwarding email to workflow coordinator: {email_data.get('subject', 'No subject')}")
            workflow_id = await self.workflow_coordinator.process_incoming_email(webhook_data)
            logger.info(f"✅ [EMAIL_MONITOR] Email forwarded successfully, workflow_id: {workflow_id}")
        else:
            logger.warning("No workflow coordinator configured, email not processed")
    
    async def test_connection(self) -> bool:
        """Test IMAP connection"""
        try:
            mail = imaplib.IMAP4_SSL(self.imap_server, self.imap_port)
            mail.login(self.email_address, self.password)
            mail.select('INBOX')
            mail.close()
            mail.logout()
            
            logger.info("Email connection test successful")
            return True
            
        except Exception as e:
            logger.error(f"Email connection test failed: {e}")
            return False
    
    async def get_recent_emails(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get recent emails for testing"""
        try:
            mail = imaplib.IMAP4_SSL(self.imap_server, self.imap_port)
            mail.login(self.email_address, self.password)
            mail.select('INBOX')
            
            # Search for recent emails
            status, messages = mail.search(None, 'ALL')
            
            emails = []
            if status == 'OK' and messages[0]:
                email_ids = messages[0].split()[-limit:]  # Get last N emails
                
                for email_id in email_ids:
                    status, msg_data = mail.fetch(email_id, '(RFC822)')
                    if status == 'OK':
                        email_body = msg_data[0][1]
                        email_message = email.message_from_bytes(email_body)
                        email_data = await self._extract_email_data(email_message)
                        emails.append(email_data)
            
            mail.close()
            mail.logout()
            
            return emails
            
        except Exception as e:
            logger.error(f"Error getting recent emails: {e}")
            return []
    
    def get_status(self) -> Dict[str, Any]:
        """Get current monitoring status"""
        return {
            "status": "running" if self.is_monitoring else "stopped",
            "service": "email_monitor",
            "email_address": self.email_address,
            "processed_count": len(self.processed_emails),
            "version": "2.0.0"
        }
