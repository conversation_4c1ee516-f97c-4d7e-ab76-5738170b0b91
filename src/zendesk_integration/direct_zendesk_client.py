"""
🎫 Direct Zendesk HTTP API Client for Zurich Claims Processing

Direct HTTP API implementation without zenpy dependency:
- Standard HTTP requests using requests library
- Intelligent priority calculation based on AI analysis
- Rich ticket descriptions with claim context
- Attachment handling with Supabase Storage links
- Comprehensive error handling and retry logic
- Real-time sync with database tracking
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, List, Tuple
import json
import time
import requests
import base64
import structlog

from ..config.settings import Settings
from ..utils.dozzle_logger import dozzle_log
from ..database.supabase_client import SupabaseClient

logger = structlog.get_logger(__name__)


class DirectZendeskClient:
    """
    Direct HTTP API Zendesk Integration for Claims Processing
    
    Features:
    - Direct HTTP API calls (no zenpy dependency)
    - Intelligent ticket creation with AI context
    - Priority calculation based on claim analysis
    - Attachment handling with Supabase Storage
    - Comprehensive error handling and retry logic
    - Database synchronization and audit trail
    """
    
    def __init__(self, settings: Settings, supabase_client: SupabaseClient):
        self.settings = settings
        self.supabase = supabase_client
        self.base_url = f"{self.settings.zendesk_url}/api/v2"
        self.session = requests.Session()
        self._initialize_client()
    
    def _initialize_client(self):
        """Initialize Zendesk client with direct HTTP API authentication"""
        try:
            # Use the zendesk_url property which constructs the full URL
            self.base_url = f"{self.settings.zendesk_url}/api/v2"
            
            # Debug logging for authentication parameters
            dozzle_log("info", "🔧 [ZENDESK] Initializing direct HTTP API client",
                      subdomain=self.settings.zendesk_subdomain,
                      email=self.settings.zendesk_email,
                      token_length=len(self.settings.zendesk_token) if self.settings.zendesk_token else 0,
                      token_preview=f"{self.settings.zendesk_token[:10]}..." if self.settings.zendesk_token else "None",
                      base_url=self.base_url)

            # Set up authentication headers using Basic Auth
            auth_string = f"{self.settings.zendesk_email}/token:{self.settings.zendesk_token}"
            auth_bytes = base64.b64encode(auth_string.encode()).decode()
            
            self.session.headers.update({
                "Authorization": f"Basic {auth_bytes}",
                "Content-Type": "application/json",
                "Accept": "application/json",
                "User-Agent": "Zurich-Claims-Processing/1.0"
            })
            
            # Test authentication immediately
            self._test_authentication()
            
        except Exception as e:
            dozzle_log("error", "❌ [ZENDESK] Failed to initialize direct HTTP client",
                      error=str(e),
                      error_type=type(e).__name__)
            raise
    
    def _test_authentication(self):
        """Test authentication by calling the /users/me endpoint"""
        try:
            dozzle_log("info", "🔍 [ZENDESK] Testing authentication with /users/me endpoint")
            
            response = self.session.get(f"{self.base_url}/users/me.json", timeout=30)
            
            dozzle_log("info", "📡 [ZENDESK] Authentication response received",
                      status_code=response.status_code,
                      response_length=len(response.text))
            
            if response.status_code == 200:
                user_data = response.json()
                user = user_data.get('user', {})
                dozzle_log("info", "✅ [ZENDESK] Direct HTTP API authentication successful",
                          user_id=user.get('id'),
                          name=user.get('name'),
                          email=user.get('email'),
                          active=user.get('active'),
                          role=user.get('role'))
                return True
            else:
                error_text = response.text[:500] if response.text else "No response text"
                dozzle_log("error", "❌ [ZENDESK] Direct HTTP API authentication failed",
                          status_code=response.status_code,
                          response_text=error_text)
                raise Exception(f"Authentication failed: {response.status_code} - {error_text}")
                
        except requests.exceptions.RequestException as e:
            dozzle_log("error", "❌ [ZENDESK] Network error during authentication test",
                      error=str(e),
                      error_type=type(e).__name__)
            raise
        except Exception as e:
            dozzle_log("error", "❌ [ZENDESK] Unexpected error during authentication test",
                      error=str(e),
                      error_type=type(e).__name__)
            raise

    def create_ticket(self, ticket_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create a ticket using direct HTTP API calls
        
        Args:
            ticket_data: Dictionary containing ticket information
            
        Returns:
            Dictionary with ticket creation result
        """
        try:
            dozzle_log("info", "🎫 [ZENDESK] Creating ticket via direct HTTP API",
                      subject=ticket_data.get('subject', 'No subject'),
                      priority=ticket_data.get('priority', 'normal'))
            
            # Prepare ticket payload according to Zendesk API v2 format
            payload = {
                "ticket": {
                    "subject": ticket_data.get('subject', 'Insurance Claim Submission'),
                    "comment": {
                        "body": ticket_data.get('description', 'No description provided'),
                        "public": ticket_data.get('public', True)
                    },
                    "priority": ticket_data.get('priority', 'normal'),
                    "type": ticket_data.get('type', 'question'),
                    "status": ticket_data.get('status', 'new'),
                    "requester": {
                        "email": ticket_data.get('requester_email'),
                        "name": ticket_data.get('requester_name', 'Insurance Claimant')
                    }
                }
            }
            
            # Add optional fields if provided
            if ticket_data.get('tags'):
                payload["ticket"]["tags"] = ticket_data["tags"]
            
            if ticket_data.get('custom_fields'):
                payload["ticket"]["custom_fields"] = ticket_data["custom_fields"]
            
            # Make the API request
            response = self.session.post(
                f"{self.base_url}/tickets.json",
                json=payload,
                timeout=30
            )
            
            dozzle_log("info", "📡 [ZENDESK] Ticket creation response received",
                      status_code=response.status_code,
                      response_length=len(response.text))
            
            if response.status_code == 201:
                # Success - ticket created
                result = response.json()
                ticket = result.get('ticket', {})
                
                ticket_info = {
                    'ticket_id': ticket.get('id'),
                    'ticket_url': ticket.get('url'),
                    'status': ticket.get('status'),
                    'created_at': ticket.get('created_at'),
                    'subject': ticket.get('subject')
                }
                
                dozzle_log("info", "✅ [ZENDESK] Ticket created successfully via direct HTTP API",
                          ticket_id=ticket_info['ticket_id'],
                          ticket_url=ticket_info['ticket_url'],
                          status=ticket_info['status'])
                
                return ticket_info
                
            else:
                # Error creating ticket
                error_text = response.text[:500] if response.text else "No response text"
                dozzle_log("error", "❌ [ZENDESK] Failed to create ticket via direct HTTP API",
                          status_code=response.status_code,
                          response_text=error_text)
                raise Exception(f"Ticket creation failed: {response.status_code} - {error_text}")
                
        except requests.exceptions.RequestException as e:
            dozzle_log("error", "❌ [ZENDESK] Network error during ticket creation",
                      error=str(e),
                      error_type=type(e).__name__)
            raise
        except Exception as e:
            dozzle_log("error", "❌ [ZENDESK] Unexpected error during ticket creation",
                      error=str(e),
                      error_type=type(e).__name__)
            raise

    def _calculate_ai_priority(self, classification_result: Dict[str, Any]) -> Tuple[str, int]:
        """
        Calculate intelligent priority based on AI analysis results
        
        Args:
            classification_result: AI classification results
            
        Returns:
            Tuple of (priority_level, priority_score)
        """
        try:
            # Extract confidence and urgency from AI analysis
            confidence = getattr(classification_result, 'confidence', 0.5)
            urgency = getattr(classification_result, 'urgency', 'MEDIUM')
            
            # Calculate base priority score
            priority_score = int(confidence * 100)
            
            # Adjust based on urgency
            urgency_multipliers = {
                'LOW': 0.7,
                'MEDIUM': 1.0,
                'HIGH': 1.3,
                'CRITICAL': 1.5
            }
            
            multiplier = urgency_multipliers.get(urgency, 1.0)
            priority_score = int(priority_score * multiplier)
            
            # Map to Zendesk priority levels
            if priority_score >= 90:
                return "urgent", priority_score
            elif priority_score >= 70:
                return "high", priority_score
            elif priority_score >= 40:
                return "normal", priority_score
            else:
                return "low", priority_score
                
        except Exception as e:
            dozzle_log("warning", "⚠️ [ZENDESK] Error calculating AI priority, using default",
                      error=str(e))
            return "normal", 50


