"""
🎫 Zendesk Integration for Zurich Claims Processing

AI-Enhanced Zendesk ticket creation with:
- Intelligent priority calculation based on AI analysis
- Rich ticket descriptions with claim context
- Attachment handling with Supabase Storage links
- Comprehensive error handling and retry logic
- Real-time sync with database tracking
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, List, Tuple
import json
import time

import requests
import base64
import structlog

from ..config.settings import Settings
from ..utils.dozzle_logger import dozzle_log
from ..database.supabase_client import SupabaseClient

logger = structlog.get_logger(__name__)


class ZendeskClient:
    """
    AI-Enhanced Zendesk Integration for Claims Processing using Direct HTTP API

    Features:
    - Direct HTTP API calls (no zenpy dependency)
    - Intelligent ticket creation with AI context
    - Priority calculation based on claim analysis
    - Attachment handling with Supabase Storage
    - Comprehensive error handling and retry logic
    - Database synchronization and audit trail
    """

    def __init__(self, settings: Settings, supabase_client: SupabaseClient):
        self.settings = settings
        self.supabase = supabase_client
        self.base_url = f"{self.settings.zendesk_url}/api/v2"
        self.session = requests.Session()
        self._use_direct_api = True  # Always use direct API now
        self._initialize_client()
    
    def _initialize_client(self):
        """Initialize Zendesk client with direct HTTP API authentication"""
        try:
            # Use the zendesk_url property which constructs the full URL
            self.base_url = f"{self.settings.zendesk_url}/api/v2"

            # Debug logging for authentication parameters
            dozzle_log("info", "🔧 [ZENDESK] Initializing direct HTTP API client",
                      subdomain=self.settings.zendesk_subdomain,
                      email=self.settings.zendesk_email,
                      token_length=len(self.settings.zendesk_token) if self.settings.zendesk_token else 0,
                      token_preview=f"{self.settings.zendesk_token[:10]}..." if self.settings.zendesk_token else "None",
                      base_url=self.base_url)

            # Set up authentication headers using Basic Auth
            auth_string = f"{self.settings.zendesk_email}/token:{self.settings.zendesk_token}"
            auth_bytes = base64.b64encode(auth_string.encode()).decode()

            self.session.headers.update({
                "Authorization": f"Basic {auth_bytes}",
                "Content-Type": "application/json",
                "Accept": "application/json",
                "User-Agent": "Zurich-Claims-Processing/1.0"
            })

            # Test authentication immediately
            self._test_authentication()

            dozzle_log("info", "✅ [ZENDESK] Direct HTTP API client initialization completed",
                      using_direct_api=True)

        except Exception as e:
            dozzle_log("error", "❌ [ZENDESK] Failed to initialize direct HTTP client",
                      error=str(e),
                      error_type=type(e).__name__)
            raise

    def _test_authentication(self):
        """Test authentication by calling the /users/me endpoint"""
        try:
            dozzle_log("info", "🔍 [ZENDESK] Testing authentication with /users/me endpoint")

            response = self.session.get(f"{self.base_url}/users/me.json", timeout=30)

            dozzle_log("info", "📡 [ZENDESK] Authentication response received",
                      status_code=response.status_code,
                      response_length=len(response.text))

            if response.status_code == 200:
                user_data = response.json()
                user = user_data.get('user', {})
                dozzle_log("info", "✅ [ZENDESK] Direct HTTP API authentication successful",
                          user_id=user.get('id'),
                          name=user.get('name'),
                          email=user.get('email'),
                          active=user.get('active'),
                          role=user.get('role'))
                return True
            else:
                error_text = response.text[:500] if response.text else "No response text"
                dozzle_log("error", "❌ [ZENDESK] Direct HTTP API authentication failed",
                          status_code=response.status_code,
                          response_text=error_text)
                raise Exception(f"Authentication failed: {response.status_code} - {error_text}")

        except requests.exceptions.RequestException as e:
            dozzle_log("error", "❌ [ZENDESK] Network error during authentication test",
                      error=str(e),
                      error_type=type(e).__name__)
            raise
        except Exception as e:
            dozzle_log("error", "❌ [ZENDESK] Unexpected error during authentication test",
                      error=str(e),
                      error_type=type(e).__name__)
            raise
    
    def _calculate_ai_priority(self, classification_result: Dict[str, Any]) -> Tuple[str, int]:
        """
        Calculate intelligent priority based on AI analysis
        
        Args:
            classification_result: AI classification results
            
        Returns:
            Tuple of (zendesk_priority, priority_score)
        """
        try:
            # Extract key factors from AI analysis
            urgency = classification_result.get('final_analysis', {}).get('urgency_level', 'medium')
            confidence = classification_result.get('consensus_confidence', 0.5)
            claim_type = classification_result.get('final_analysis', {}).get('claim_type', 'general')
            estimated_value = classification_result.get('final_analysis', {}).get('estimated_value', 0)
            
            # Base priority score calculation
            priority_score = 50  # Base score
            
            # Urgency factor (0-30 points)
            urgency_scores = {
                'critical': 30,
                'high': 20,
                'medium': 10,
                'low': 0
            }
            priority_score += urgency_scores.get(urgency.lower(), 10)
            
            # Confidence factor (0-20 points)
            priority_score += int(confidence * 20)
            
            # Claim type factor (0-20 points)
            high_priority_types = ['liability', 'workers_comp', 'auto']
            if claim_type.lower() in high_priority_types:
                priority_score += 15
            elif claim_type.lower() in ['property']:
                priority_score += 10
            else:
                priority_score += 5
            
            # Estimated value factor (0-30 points)
            if estimated_value > 100000:
                priority_score += 30
            elif estimated_value > 50000:
                priority_score += 20
            elif estimated_value > 10000:
                priority_score += 10
            else:
                priority_score += 5
            
            # Convert to Zendesk priority
            if priority_score >= 90:
                zendesk_priority = "urgent"
            elif priority_score >= 70:
                zendesk_priority = "high"
            elif priority_score >= 40:
                zendesk_priority = "normal"
            else:
                zendesk_priority = "low"
            
            dozzle_log("info", "🎯 [ZENDESK] AI priority calculated",
                      urgency=urgency,
                      confidence=confidence,
                      claim_type=claim_type,
                      estimated_value=estimated_value,
                      priority_score=priority_score,
                      zendesk_priority=zendesk_priority)
            
            return zendesk_priority, priority_score
            
        except Exception as e:
            dozzle_log("warning", "⚠️ [ZENDESK] Priority calculation failed, using default",
                      error=str(e))
            return "normal", 50
    
    def _generate_ai_enhanced_description(self, 
                                        email_data: Dict[str, Any],
                                        classification_result: Dict[str, Any],
                                        attachments: List[Dict[str, Any]] = None) -> str:
        """
        Generate comprehensive ticket description with AI insights
        
        Args:
            email_data: Original email information
            classification_result: AI analysis results
            attachments: List of attachment information
            
        Returns:
            Rich HTML description for Zendesk ticket
        """
        try:
            final_analysis = classification_result.get('final_analysis', {})
            consensus_data = classification_result.get('consensus_data', {})
            
            # Build comprehensive description
            description_parts = []
            
            # Header with AI classification
            description_parts.append("🤖 **AI-Enhanced Claim Processing**")
            description_parts.append("=" * 50)
            description_parts.append("")
            
            # Original email information
            description_parts.append("📧 **Original Email Information**")
            description_parts.append(f"**From:** {email_data.get('sender_email', 'Unknown')}")
            description_parts.append(f"**Subject:** {email_data.get('subject', 'No Subject')}")
            description_parts.append(f"**Received:** {email_data.get('received_at', datetime.utcnow().isoformat())}")
            description_parts.append("")
            
            # AI Classification Results
            description_parts.append("🎯 **AI Classification Results**")
            description_parts.append(f"**Claim Type:** {final_analysis.get('claim_type', 'Unknown')}")
            description_parts.append(f"**Urgency Level:** {final_analysis.get('urgency_level', 'Medium')}")
            description_parts.append(f"**Confidence Level:** {final_analysis.get('confidence_level', 'Medium')}")
            description_parts.append(f"**Consensus Confidence:** {classification_result.get('consensus_confidence', 0.0):.1%}")
            description_parts.append("")
            
            # Extracted claim details
            if final_analysis.get('extracted_details'):
                details = final_analysis['extracted_details']
                description_parts.append("📋 **Extracted Claim Details**")
                
                if details.get('policy_number'):
                    description_parts.append(f"**Policy Number:** {details['policy_number']}")
                if details.get('incident_date'):
                    description_parts.append(f"**Incident Date:** {details['incident_date']}")
                if details.get('incident_location'):
                    description_parts.append(f"**Incident Location:** {details['incident_location']}")
                if details.get('claimant_name'):
                    description_parts.append(f"**Claimant:** {details['claimant_name']}")
                if details.get('estimated_value'):
                    description_parts.append(f"**Estimated Value:** ${details['estimated_value']:,.2f}")
                
                description_parts.append("")
            
            # Model consensus information
            if consensus_data:
                description_parts.append("🤖 **AI Model Consensus**")
                description_parts.append(f"**Primary Model (GPT-4o):** {consensus_data.get('primary_confidence', 0.0):.1%} confidence")
                description_parts.append(f"**Validation Model (GPT-4o Mini):** {consensus_data.get('validation_confidence', 0.0):.1%} confidence")
                description_parts.append(f"**Agreement Score:** {consensus_data.get('agreement_score', 0.0):.1%}")
                description_parts.append("")
            
            # Attachments information
            if attachments:
                description_parts.append("📎 **Attachments**")
                for attachment in attachments:
                    description_parts.append(f"• **{attachment['filename']}** ({attachment['content_type']}, {attachment['file_size']:,} bytes)")
                    if attachment.get('storage_url'):
                        description_parts.append(f"  🔗 [View File]({attachment['storage_url']})")
                description_parts.append("")
            
            # Original email content
            description_parts.append("📝 **Original Email Content**")
            description_parts.append("```")
            description_parts.append(email_data.get('body', 'No email body available'))
            description_parts.append("```")
            description_parts.append("")
            
            # Processing metadata
            description_parts.append("⚙️ **Processing Information**")
            description_parts.append(f"**Workflow ID:** {email_data.get('workflow_id', 'Unknown')}")
            description_parts.append(f"**Processing Time:** {datetime.utcnow().isoformat()}")
            description_parts.append(f"**System:** Zurich AI Claims Processing v1.0")
            
            return "\n".join(description_parts)
            
        except Exception as e:
            dozzle_log("warning", "⚠️ [ZENDESK] Description generation failed, using fallback",
                      error=str(e))
            
            # Fallback description
            return f"""
🤖 AI-Enhanced Claim Processing

📧 Email from: {email_data.get('sender_email', 'Unknown')}
📝 Subject: {email_data.get('subject', 'No Subject')}
🎯 Claim Type: {classification_result.get('final_analysis', {}).get('claim_type', 'Unknown')}

Original Email:
{email_data.get('body', 'No email body available')}

Workflow ID: {email_data.get('workflow_id', 'Unknown')}
"""
    
    def _extract_email_address(self, email_string: str) -> str:
        """
        Extract clean email address from potentially formatted email string

        Args:
            email_string: Email string that might contain display name

        Returns:
            Clean email address only
        """
        import re

        if not email_string:
            raise ValueError("Email address is required - cannot create ticket without sender email")

        # Remove any leading/trailing whitespace
        email_string = email_string.strip()

        # <AUTHOR> <EMAIL>" format
        email_pattern = r'<([^>]+)>'
        match = re.search(email_pattern, email_string)

        if match:
            # Found email in angle brackets
            return match.group(1).strip()

        # Pattern to match standalone email address
        standalone_email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
        match = re.search(standalone_email_pattern, email_string)

        if match:
            return match.group(0).strip()

        # If no valid email found, raise error instead of using fallback
        raise ValueError(f"Could not extract valid email address from: {email_string}")

    async def create_simple_ticket(self,
                                 claim_id: str,
                                 workflow_id: str,
                                 email_data: Dict[str, Any],
                                 attachments: List[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Create professional Zendesk ticket with clean formatting for insurance claims

        Args:
            claim_id: UUID of the claim
            workflow_id: Workflow ID from email processing
            email_data: Original email information
            attachments: List of attachment information

        Returns:
            Created ticket information with database sync
        """
        try:
            dozzle_log("info", "🎫 [ZENDESK] Creating professional insurance claim ticket",
                      claim_id=claim_id,
                      workflow_id=workflow_id,
                      sender=email_data.get('sender_email'))

            # Extract core email information
            original_subject = email_data.get('subject', 'Insurance Claim Submission')
            original_body = email_data.get('body', 'No email content available')
            raw_sender_email = email_data.get('sender_email')
            sender_name = email_data.get('sender_name', email_data.get('from_name', ''))
            received_at = email_data.get('received_at', '')
            
            # Validate required sender email
            if not raw_sender_email:
                raise ValueError("Sender email is required to create ticket")

            # Extract clean email address
            sender_email = self._extract_email_address(raw_sender_email)

            # Generate intelligent claim reference with meaningful prefix
            claim_ref = self._generate_claim_reference(claim_id, email_data)

            # Professional subject with claim reference and key details
            subject = self._generate_professional_subject(claim_ref, original_subject, email_data)

            # Determine appropriate tags based on email content
            tags = self._generate_professional_tags(email_data, attachments)

            # Professional ticket description - PUBLIC COMMENT
            description = self._generate_public_description(
                claim_ref, sender_name, sender_email, received_at, 
                original_subject, original_body, attachments
            )

            # Upload attachments first if present
            uploaded_attachments = []
            attachment_names = []
            if attachments:
                dozzle_log("info", "📎 [ZENDESK] Processing attachments for ticket",
                          attachment_count=len(attachments),
                          claim_id=claim_id)

                for attachment in attachments:
                    filename = attachment.get('filename', 'Unknown file')
                    attachment_names.append(filename)
                    uploaded_token = await self._upload_attachment(attachment)
                    if uploaded_token:
                        uploaded_attachments.append(uploaded_token)
                        dozzle_log("info", "✅ [ZENDESK] Attachment uploaded successfully",
                                  filename=filename,
                                  claim_id=claim_id)
                    else:
                        dozzle_log("error", "❌ [ZENDESK] Failed to upload attachment",
                                  filename=filename,
                                  claim_id=claim_id)

            # Create professional ticket with attachments
            ticket_data = await self._create_ticket_with_retry({
                "subject": subject,
                "description": description,
                "requester_email": sender_email,
                "requester_name": sender_name or "Insurance Claimant",
                "priority": "normal",
                "type": "question",
                "tags": tags,
                "uploads": uploaded_attachments if uploaded_attachments else None
            })

            # Store ticket information in database
            await self._store_ticket_in_database(
                ticket_data=ticket_data,
                workflow_id=workflow_id,
                claim_id=claim_id
            )

            dozzle_log("info", "✅ [ZENDESK] Professional insurance claim ticket created",
                      ticket_id=ticket_data.get('id'),
                      claim_ref=claim_ref,
                      subject=subject,
                      tags=tags,
                      attachments_uploaded=len(uploaded_attachments),
                      attachment_names=attachment_names if attachment_names else "None")

            return ticket_data

        except Exception as e:
            dozzle_log("error", "❌ [ZENDESK] Failed to create professional claim ticket",
                      claim_id=claim_id,
                      workflow_id=workflow_id,
                      error=str(e))
            raise

    def _generate_professional_tags(self, email_data: Dict[str, Any], attachments: List[Dict[str, Any]] = None) -> List[str]:
        """Generate appropriate tags for insurance claim tickets"""
        tags = ["insurance_claim", "claim_submission"]
        
        # Email content analysis for intelligent tagging
        subject = email_data.get('subject', '').lower()
        body = email_data.get('body', '').lower()
        combined_text = f"{subject} {body}"
        
        # Claim type tags - following insurance industry standards
        if any(keyword in combined_text for keyword in ['injury', 'hurt', 'pain', 'slip', 'fall']):
            tags.append("personal_injury")
        elif any(keyword in combined_text for keyword in ['auto', 'car', 'vehicle', 'accident', 'collision', 'crash']):
            tags.append("auto_claim")
        elif any(keyword in combined_text for keyword in ['home', 'house', 'property', 'fire', 'flood', 'damage', 'roof']):
            tags.append("property_claim")
        elif any(keyword in combined_text for keyword in ['liability', 'legal', 'lawsuit', 'responsible']):
            tags.append("liability_claim")
        else:
            tags.append("general_claim")
        
        # Priority tags
        if any(keyword in combined_text for keyword in ['urgent', 'emergency', 'immediate', 'asap', 'critical']):
            tags.append("high_priority")
        elif any(keyword in combined_text for keyword in ['total loss', 'major damage', 'serious', 'significant']):
            tags.append("major_claim")
        else:
            tags.append("standard_priority")
        
        # Attachment status tags
        if attachments and len(attachments) > 0:
            tags.append("has_attachments")
            if len(attachments) > 2:
                tags.append("multiple_attachments")
        else:
            # Check if attachments were mentioned but not received
            if any(keyword in combined_text for keyword in ['attachment', 'attached', 'document', 'file', 'report', 'form']):
                tags.append("awaiting_attachments")
            else:
                tags.append("no_attachments")
        
        # Source tags
        tags.append("email_submission")
        
        return tags

    def _generate_claim_reference(self, claim_id: str, email_data: Dict[str, Any]) -> str:
        """Generate intelligent claim reference with meaningful prefix"""
        # Define claim type prefixes
        CLAIM_PREFIXES = {
            "personal_injury": "PI",
            "liability": "LI", 
            "auto_claim": "AUTO",
            "auto_accident": "AUTO",
            "property_claim": "PD",
            "property_damage": "PD",
            "medical_claim": "MED",
            "general_claim": "CL",
            "general": "CL"
        }
        
        # Determine claim type from email content
        subject = email_data.get('subject', '').lower()
        body = email_data.get('body', '').lower()
        combined_text = f"{subject} {body}"
        
        # Analyze content for claim type
        prefix = "CL"  # Default prefix
        
        if any(keyword in combined_text for keyword in ['injury', 'hurt', 'pain', 'medical', 'hospital', 'doctor', 'slip', 'fall']):
            prefix = "PI"  # Personal Injury
        elif any(keyword in combined_text for keyword in ['auto', 'car', 'vehicle', 'accident', 'collision', 'crash']):
            prefix = "AUTO"  # Auto Accident
        elif any(keyword in combined_text for keyword in ['property', 'home', 'house', 'fire', 'flood', 'damage', 'roof']):
            prefix = "PD"  # Property Damage
        elif any(keyword in combined_text for keyword in ['liability', 'legal', 'lawsuit', 'responsible']):
            prefix = "LI"  # Liability
        
        # Generate unique suffix from claim_id
        if claim_id:
            unique_suffix = claim_id.replace('-', '').upper()[:8]
        else:
            import uuid
            unique_suffix = str(uuid.uuid4()).replace('-', '').upper()[:8]
        
        return f"{prefix}{unique_suffix}"

    def _generate_professional_subject(self, claim_ref: str, original_subject: str, email_data: Dict[str, Any]) -> str:
        """Generate professional subject with key details"""
        # Extract incident date if available
        body = email_data.get('body', '')
        
        # Try to find date patterns in the email
        import re
        date_patterns = [
            r'(\w+ \d{1,2}, \d{4})',  # "August 27, 2020"
            r'(\d{1,2}/\d{1,2}/\d{4})',  # "08/27/2020"
            r'(\d{4}-\d{2}-\d{2})',  # "2020-08-27"
        ]
        
        incident_date = None
        for pattern in date_patterns:
            match = re.search(pattern, body)
            if match:
                incident_date = match.group(1)
                break
        
        # Clean up subject and add key details
        clean_subject = original_subject.replace('Re:', '').replace('FW:', '').strip()
        
        if incident_date:
            return f"[{claim_ref}] Claim Submission - {clean_subject} ({incident_date})"
        else:
            return f"[{claim_ref}] Claim Submission - {clean_subject}"

    def _generate_public_description(self, claim_ref: str, sender_name: str, sender_email: str, 
                                   received_at: str, original_subject: str, original_body: str, 
                                   attachments: List[Dict[str, Any]]) -> str:
        """Generate professional public description"""
        
        # Extract sender name from email if not provided
        if not sender_name:
            if '<' in sender_email:
                sender_name = sender_email.split('<')[0].strip().strip('"')
            else:
                sender_name = sender_email.split('@')[0].replace('.', ' ').title()
        
        description = f"""**Subject:** {original_subject}

Dear Zurich Claims Team,

We have received a claim submission from **{sender_name}** regarding the incident described below.

**Original Message:**
{original_body}"""

        # Handle attachments professionally
        if attachments and len(attachments) > 0:
            description += f"""

**Documents submitted ({len(attachments)} files):**"""
            for attachment in attachments:
                filename = attachment.get('filename', 'Unknown file')
                description += f"\n- {filename}"
        else:
            # Check if attachments were mentioned in email but not received
            if any(keyword in original_body.lower() for keyword in ['attach', 'document', 'file', 'report', 'form']):
                description += f"""

> **Note:** Attachments were mentioned in the email but were not included."""

        description += f"""

A new claim has been logged with internal reference **`{claim_ref.upper()}`**.

Thank you,
Zurich Claims Processing System"""

        return description

    async def _upload_attachment(self, attachment: Dict[str, Any]) -> Optional[str]:
        """Upload attachment to Zendesk and return token"""
        try:
            filename = attachment.get('filename', 'attachment')
            content = attachment.get('content')
            content_type = attachment.get('content_type', 'application/octet-stream')
            
            if not content:
                dozzle_log("warning", "⚠️ [ZENDESK] Attachment has no content, skipping",
                          filename=filename)
                return None
            
            # Prepare file data for upload
            if isinstance(content, str):
                # Base64 encoded content
                import base64
                file_data = base64.b64decode(content)
            else:
                # Binary content
                file_data = content
            
            dozzle_log("info", "📎 [ZENDESK] Uploading attachment",
                      filename=filename,
                      content_type=content_type,
                      size_bytes=len(file_data))
            
            # Upload to Zendesk using correct API format
            response = self.session.post(
                f"{self.base_url}/uploads",
                data=file_data,
                headers={'Content-Type': content_type},
                params={'filename': filename},
                timeout=60  # Longer timeout for file uploads
            )
            
            if response.status_code == 201:
                result = response.json()
                upload_token = result.get('upload', {}).get('token')
                upload_url = result.get('upload', {}).get('attachment', {}).get('content_url')
                
                dozzle_log("info", "✅ [ZENDESK] Attachment uploaded successfully",
                          filename=filename,
                          token=upload_token[:20] + "..." if upload_token else None,
                          content_url=upload_url[:50] + "..." if upload_url else None)
                return upload_token
            else:
                error_text = response.text[:200] if response.text else "No error text"
                dozzle_log("error", "❌ [ZENDESK] Failed to upload attachment",
                          filename=filename,
                          status_code=response.status_code,
                          error=error_text)
                return None
                
        except Exception as e:
            dozzle_log("error", "❌ [ZENDESK] Exception during attachment upload",
                      filename=attachment.get('filename', 'unknown'),
                      error=str(e))
            return None

    async def _store_ticket_in_database(self, ticket_data: Dict[str, Any], workflow_id: str, claim_id: Optional[str]) -> None:
        """Store ticket information in Supabase database"""
        try:
            if ticket_data and ticket_data.get('id'):
                # Store basic ticket information
                await self.supabase.create_zendesk_ticket_record({
                    'claim_id': claim_id,
                    'zendesk_ticket_id': str(ticket_data['id']),
                    'zendesk_url': f"{self.settings.zendesk_url}/agent/tickets/{ticket_data['id']}",
                    'subject': ticket_data.get('subject', ''),
                    'status': ticket_data.get('status', 'new'),
                    'priority': ticket_data.get('priority', 'normal'),
                    'sync_status': 'synced'
                })

                dozzle_log("info", "✅ [ZENDESK] Ticket stored in database",
                          ticket_id=ticket_data['id'],
                          claim_id=claim_id,
                          workflow_id=workflow_id)
        except Exception as e:
            dozzle_log("error", "❌ [ZENDESK] Failed to store ticket in database",
                      error=str(e))
            # Don't raise - ticket was created successfully, database storage is secondary

    async def _create_ticket_with_retry(self, ticket_data: Dict[str, Any], max_retries: int = 3) -> Optional[Dict[str, Any]]:
        """
        Create Zendesk ticket with exponential backoff retry logic using direct HTTP API

        Args:
            ticket_data: Ticket information
            max_retries: Maximum number of retry attempts

        Returns:
            Created ticket information dict or None if failed
        """
        for attempt in range(max_retries + 1):
            try:
                dozzle_log("info", f"🎫 [ZENDESK] Creating ticket via direct HTTP API (attempt {attempt + 1})",
                          subject=ticket_data.get('subject', 'No subject'),
                          priority=ticket_data.get('priority', 'normal'),
                          attempt=attempt + 1,
                          max_retries=max_retries)

                # Prepare ticket payload according to Zendesk API v2 format
                payload = {
                    "ticket": {
                        "subject": ticket_data.get('subject', 'Insurance Claim Submission'),
                        "comment": {
                            "body": ticket_data.get('description', 'No description provided'),
                            "public": ticket_data.get('public', True)
                        },
                        "priority": ticket_data.get('priority', 'normal'),
                        "type": ticket_data.get('type', 'question'),
                        "status": ticket_data.get('status', 'new'),
                        "requester": {
                            "email": ticket_data.get('requester_email'),
                            "name": ticket_data.get('requester_name', 'Insurance Claimant')
                        }
                    }
                }

                # Add optional fields if provided
                if ticket_data.get('tags'):
                    payload["ticket"]["tags"] = ticket_data["tags"]

                if ticket_data.get('custom_fields'):
                    payload["ticket"]["custom_fields"] = ticket_data["custom_fields"]
                
                # Add uploaded attachments if present
                if ticket_data.get('uploads'):
                    payload["ticket"]["comment"]["uploads"] = ticket_data["uploads"]

                # Make the API request
                response = self.session.post(
                    f"{self.base_url}/tickets.json",
                    json=payload,
                    timeout=30
                )

                dozzle_log("info", "📡 [ZENDESK] Ticket creation response received",
                          status_code=response.status_code,
                          response_length=len(response.text),
                          attempt=attempt + 1)

                if response.status_code == 201:
                    # Success - ticket created
                    result = response.json()
                    ticket = result.get('ticket', {})

                    ticket_info = {
                        'id': ticket.get('id'),
                        'url': ticket.get('url'),
                        'status': ticket.get('status'),
                        'created_at': ticket.get('created_at'),
                        'subject': ticket.get('subject'),
                        'priority': ticket.get('priority'),
                        'type': ticket.get('type')
                    }

                    dozzle_log("info", "✅ [ZENDESK] Ticket created successfully via direct HTTP API",
                              ticket_id=ticket_info['id'],
                              ticket_url=ticket_info['url'],
                              status=ticket_info['status'],
                              attempt=attempt + 1)

                    return ticket_info

                else:
                    # Error creating ticket
                    error_text = response.text[:500] if response.text else "No response text"
                    raise Exception(f"HTTP {response.status_code}: {error_text}")

            except requests.exceptions.RequestException as e:
                dozzle_log("warning", f"⚠️ [ZENDESK] Network error on attempt {attempt + 1}",
                          error=str(e),
                          error_type=type(e).__name__,
                          attempt=attempt + 1,
                          max_retries=max_retries)

                if attempt < max_retries:
                    # Exponential backoff: 2^attempt seconds
                    wait_time = 2 ** attempt
                    dozzle_log("info", f"🔄 [ZENDESK] Retrying in {wait_time} seconds",
                              wait_time=wait_time,
                              attempt=attempt + 1)
                    await asyncio.sleep(wait_time)
                else:
                    dozzle_log("error", "❌ [ZENDESK] All retry attempts exhausted (network errors)",
                              max_retries=max_retries)
                    raise

            except Exception as e:
                dozzle_log("warning", f"⚠️ [ZENDESK] Unexpected error on attempt {attempt + 1}",
                          error=str(e),
                          error_type=type(e).__name__,
                          attempt=attempt + 1,
                          max_retries=max_retries)

                if attempt < max_retries:
                    wait_time = 2 ** attempt
                    dozzle_log("info", f"🔄 [ZENDESK] Retrying in {wait_time} seconds",
                              wait_time=wait_time,
                              attempt=attempt + 1)
                    await asyncio.sleep(wait_time)
                else:
                    dozzle_log("error", "❌ [ZENDESK] All retry attempts exhausted",
                              max_retries=max_retries)
                    raise

        return None

    async def add_ai_analysis_comment(self,
                                    ticket_id: str,
                                    classification_result: Dict[str, Any],
                                    claim_id: str,
                                    email_data: Dict[str, Any] = None,
                                    attachments: List[Dict[str, Any]] = None) -> bool:
        """
        Add AI analysis as second comment to professional insurance claim ticket

        Args:
            ticket_id: Zendesk ticket ID
            classification_result: AI analysis results
            claim_id: Claim ID for reference
            email_data: Original email information (optional)
            attachments: List of attachments (optional)

        Returns:
            Success status
        """
        try:
            dozzle_log("info", "🤖 [ZENDESK] Adding AI analysis comment to ticket",
                      ticket_id=ticket_id,
                      claim_id=claim_id)

            # Extract AI analysis details
            final_analysis = classification_result.get('final_analysis', {})
            extracted_details = final_analysis.get('extracted_details', {})
            consensus_confidence = classification_result.get('consensus_confidence', 0.0)
            
            # Handle missing email_data
            if email_data is None:
                email_data = {'body': '', 'subject': 'Insurance Claim'}

            # Extract formatted values outside f-string to avoid backslash issues
            claim_type_display = final_analysis.get('claim_type', 'general').replace('_', ' ')
            claim_type_summary = final_analysis.get('claim_type', 'general').replace('_', '-')
            claim_type_title = final_analysis.get('claim_type', 'General').replace('_', ' ').title()
            
            # Extract more formatted values to avoid backslash issues in f-strings
            documents_status = 'all' if attachments else 'mentioned'
            attachment_status = 'attached' if attachments else 'referenced'
            urgency_level = final_analysis.get('urgency_level', 'medium').lower()
            urgency_connector = 'due to' if final_analysis.get('urgency_level', '').upper() in ['HIGH', 'CRITICAL'] else 'with'
            compensation_text = 'request for compensation and coverage' if 'compensation' in email_data.get('body', '').lower() else 'formal submission requirements'
            review_status = 'not required unless discrepancies arise' if consensus_confidence >= 0.85 else 'recommended due to confidence level'

            # Check for attachments mentioned in email
            attachments_mentioned = 'YES' if any(keyword in email_data.get('body', '').lower() for keyword in ['attach', 'document', 'file', 'report', 'form']) else 'NO'
            attachments_received = 'YES' if attachments else 'NO'
            immediate_action = 'YES' if final_analysis.get('urgency_level', '').upper() in ['HIGH', 'CRITICAL'] else 'NO'
            human_review = 'YES' if consensus_confidence < 0.85 else 'NO'

            # Check for medical attention
            medical_status = 'required' if 'medical' in email_data.get('body', '').lower() else 'status unknown'

            # Extract documents list
            body_text = email_data.get('body', '')
            documents_list = ', '.join([d.strip() for d in body_text.split('\n') if d.strip().startswith('•')]) if '•' in body_text else 'documentation submitted'

            # Build incident date text
            incident_date_text = f" that occurred on {extracted_details.get('incident_date')}" if extracted_details.get('incident_date') else ""

            # Build professional AI analysis comment matching user requirements
            ai_comment = f"""🔍 AI Claim Classification Summary

Email Type: {final_analysis.get('email_type', 'CLAIM_SUBMISSION')}
Claim Type: {final_analysis.get('claim_type', 'GENERAL').upper()}
Urgency Level: {final_analysis.get('urgency_level', 'STANDARD').upper()}
Confidence: {final_analysis.get('confidence_level', 'MEDIUM').upper()} ({consensus_confidence:.2f})
Incident Date: {extracted_details.get('incident_date', 'Not specified')}
Location: {extracted_details.get('incident_location', 'Not specified')}
Attachments Mentioned: {attachments_mentioned}
Attachments Received: {attachments_received}
Requires Immediate Action: {immediate_action}
Requires Human Review: {human_review}

Summary: Customer submitted documents related to a {claim_type_summary} incident{incident_date_text}.

Key Details:
- {claim_type_title} claim at {extracted_details.get('incident_location', 'unspecified location')}
- Medical attention {medical_status}
- Documents: {documents_list}

AI Reasoning:
The email is a structured and complete claim submission with {documents_status} supporting documents {attachment_status}. The claim type is clearly {claim_type_display}, and urgency is {urgency_level} {urgency_connector} {compensation_text}. Human review {review_status}."""

            # Add the comment to the ticket
            result = await self.add_comment_to_ticket(
                ticket_id=ticket_id,
                comment=ai_comment,
                public=False  # Internal comment
            )

            dozzle_log("info", "✅ [ZENDESK] AI analysis comment added successfully",
                      ticket_id=ticket_id,
                      claim_id=claim_id,
                      confidence=consensus_confidence)

            return result

        except Exception as e:
            dozzle_log("error", "❌ [ZENDESK] Failed to add AI analysis comment",
                      ticket_id=ticket_id,
                      claim_id=claim_id,
                      error=str(e))
            return False

    async def add_comment_to_ticket(self,
                                  ticket_id: str,
                                  comment: str,
                                  public: bool = True) -> bool:
        """
        Add a comment to an existing Zendesk ticket using direct HTTP API

        Args:
            ticket_id: Zendesk ticket ID
            comment: Comment text to add
            public: Whether comment is public (default: True) or internal (False)

        Returns:
            True if successful, False otherwise
        """
        try:
            dozzle_log("info", "💬 [ZENDESK] Adding comment to ticket via direct HTTP API",
                      ticket_id=ticket_id,
                      public=public,
                      comment_length=len(comment))

            # Prepare comment payload
            payload = {
                "ticket": {
                    "comment": {
                        "body": comment,
                        "public": public
                    }
                }
            }

            # Make the API request to update the ticket with comment
            response = self.session.put(
                f"{self.base_url}/tickets/{ticket_id}.json",
                json=payload,
                timeout=30
            )

            if response.status_code == 200:
                dozzle_log("info", "✅ [ZENDESK] Comment added successfully via direct HTTP API",
                          ticket_id=ticket_id,
                          public=public)
                return True
            else:
                error_text = response.text[:500] if response.text else "No response text"
                dozzle_log("error", "❌ [ZENDESK] Failed to add comment via direct HTTP API",
                          ticket_id=ticket_id,
                          status_code=response.status_code,
                          response_text=error_text)
                return False

        except Exception as e:
            dozzle_log("error", "❌ [ZENDESK] Failed to add comment to ticket",
                      ticket_id=ticket_id,
                      error=str(e),
                      error_type=type(e).__name__)
            return False
