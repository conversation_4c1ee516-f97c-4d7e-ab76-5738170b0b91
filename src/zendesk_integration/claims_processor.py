"""
🏭 Claims Processing Service - Zendesk + Supabase Integration

Comprehensive service that orchestrates:
- Claim creation in Supabase database
- Attachment processing and storage
- AI-enhanced Zendesk ticket creation
- Complete audit trail and error handling
"""

import asyncio
import logging
from datetime import datetime
from typing import Optional, Dict, Any, List, Tuple
import uuid
import json

import structlog
from email.mime.multipart import MIMEMult<PERSON>art
from email.mime.base import MIMEBase

from ..config.settings import Settings
from ..database.supabase_client import SupabaseClient
from .zendesk_client import ZendeskClient
from ..utils.dozzle_logger import dozzle_log

logger = structlog.get_logger(__name__)


class ClaimsProcessor:
    """
    Comprehensive Claims Processing Service
    
    Orchestrates the complete flow from email classification to Zendesk ticket creation:
    1. Create claim record in Supabase
    2. Process and store attachments
    3. Create AI-enhanced Zendesk ticket
    4. Maintain complete audit trail
    """
    
    def __init__(self, settings: Settings):
        self.settings = settings
        self.supabase = SupabaseClient(settings)
        self.zendesk = ZendeskClient(settings, self.supabase)
    
    async def process_claim_email(self,
                                workflow_id: str,
                                email_data: Dict[str, Any],
                                classification_result: Dict[str, Any],
                                attachments: List[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Complete claim processing workflow
        
        Args:
            workflow_id: Workflow ID from email processing
            email_data: Original email information
            classification_result: AI analysis results
            attachments: List of email attachments (optional)
            
        Returns:
            Complete processing result with claim_id, ticket_id, and status
        """
        claim_id = None
        processing_result = {
            'success': False,
            'claim_id': None,
            'zendesk_ticket_id': None,
            'zendesk_ticket_url': None,
            'attachments_processed': 0,
            'errors': []
        }
        
        try:
            dozzle_log("info", "🏭 [CLAIMS_PROCESSOR] Starting claim processing workflow",
                      workflow_id=workflow_id,
                      sender=email_data.get('sender_email'),
                      has_attachments=bool(attachments))
            
            # Step 1: Create claim record in Supabase FIRST to get claim_id
            claim_id = await self._create_claim_record(
                workflow_id, email_data, classification_result,
                zendesk_ticket_id=None,  # Will be updated after ticket creation
                zendesk_ticket_url=None
            )
            processing_result['claim_id'] = claim_id

            dozzle_log("info", "📝 [CLAIMS_PROCESSOR] Claim record created",
                      claim_id=claim_id,
                      workflow_id=workflow_id,
                      claim_type=classification_result.get('claim_type'))

            # Step 2: Create professional Zendesk ticket WITH the actual claim_id
            ticket_result = await self._create_zendesk_ticket(
                claim_id, workflow_id, email_data, classification_result, []  # Now we have claim_id
            )

            processing_result.update({
                'zendesk_ticket_id': ticket_result.get('id'),  # Zendesk API returns 'id', not 'ticket_id'
                'zendesk_ticket_url': ticket_result.get('url'),  # Zendesk API returns 'url', not 'ticket_url'
                'zendesk_priority': ticket_result.get('priority'),
                'ai_priority_score': ticket_result.get('priority_score', 0.0)  # Default if not present
            })

            dozzle_log("info", "🎫 [CLAIMS_PROCESSOR] Professional Zendesk ticket created successfully",
                      workflow_id=workflow_id,
                      ticket_id=ticket_result.get('id'),
                      ticket_url=ticket_result.get('url'))

            # Step 2.1: Add AI analysis as second comment
            await self.zendesk.add_ai_analysis_comment(
                ticket_id=ticket_result.get('id'),
                classification_result=classification_result,
                claim_id=claim_id,
                email_data=email_data,
                attachments=attachments
            )

            # Step 2.5: Update claim record with Zendesk ticket information
            await self.supabase.update_claim(
                claim_id=claim_id,
                updates={
                    'zendesk_ticket_id': ticket_result.get('id'),
                    'zendesk_ticket_url': ticket_result.get('url')
                }
            )

            # Step 3: Process attachments if present and update Zendesk ticket
            processed_attachments = []
            if attachments:
                processed_attachments = await self._process_attachments(claim_id, workflow_id, attachments)
                processing_result['attachments_processed'] = len(processed_attachments)

                # Update Zendesk ticket with attachment information
                await self._update_zendesk_ticket_with_attachments(
                    ticket_result.get('id'), processed_attachments
                )
            
            # Step 5: Add completion to audit trail
            await self.supabase.add_claim_history(
                claim_id=claim_id,
                event_type="claim_processing_completed",
                description="Complete claim processing workflow finished successfully",
                new_values={
                    'status': 'processing',
                    'zendesk_ticket_id': ticket_result.get('id'),
                    'attachments_count': len(processed_attachments)
                },
                metadata={
                    'workflow_id': workflow_id,
                    'processing_duration': 'completed',
                    'ai_priority_score': ticket_result.get('priority_score', 0)  # Safe access with default
                }
            )
            
            processing_result['success'] = True
            
            dozzle_log("info", "✅ [CLAIMS_PROCESSOR] Claim processing completed successfully",
                      claim_id=claim_id,
                      workflow_id=workflow_id,
                      zendesk_ticket_id=ticket_result.get('id'),
                      attachments_processed=len(processed_attachments),
                      priority=ticket_result.get('priority'))
            
            return processing_result
            
        except Exception as e:
            error_msg = f"Claim processing failed: {str(e)}"
            processing_result['errors'].append(error_msg)
            
            dozzle_log("error", "❌ [CLAIMS_PROCESSOR] Claim processing failed",
                      claim_id=claim_id,
                      workflow_id=workflow_id,
                      error=str(e))
            
            # Add error to audit trail if claim was created
            if claim_id:
                try:
                    await self.supabase.add_claim_history(
                        claim_id=claim_id,
                        event_type="claim_processing_failed",
                        description=error_msg,
                        metadata={
                            'workflow_id': workflow_id,
                            'error': str(e),
                            'processing_step': 'unknown'
                        }
                    )
                    
                    # Update claim status to indicate failure
                    await self.supabase.update_claim(claim_id, {
                        'status': 'failed'
                    })
                except:
                    pass  # Don't fail if audit logging fails
            
            raise
    
    async def _create_claim_record(self,
                                 workflow_id: str,
                                 email_data: Dict[str, Any],
                                 classification_result: Dict[str, Any],
                                 zendesk_ticket_id: str = None,
                                 zendesk_ticket_url: str = None) -> str:
        """
        Create comprehensive claim record in Supabase
        
        Args:
            workflow_id: Workflow ID from email processing
            email_data: Original email information
            classification_result: AI analysis results
            
        Returns:
            Created claim_id (UUID)
        """
        try:
            dozzle_log("info", "📝 [CLAIMS_PROCESSOR] Creating claim record",
                      workflow_id=workflow_id)
            
            # Extract AI analysis results
            final_analysis = classification_result.get('final_analysis', {})
            extracted_details = final_analysis.get('extracted_details', {})
            
            # Build comprehensive claim data with validation
            sender_email = email_data.get('sender_email', '').strip()
            if not sender_email:
                raise ValueError("Sender email is required for claim creation")
            
            # Extract sender name with better logic
            sender_name = email_data.get('sender_name', '') or email_data.get('from_name', '')
            if not sender_name and sender_email:
                # Generate name from email if not provided
                name_part = sender_email.split('@')[0]
                sender_name = name_part.replace('.', ' ').replace('_', ' ').title()
            
            # Clean and validate email subject
            email_subject = email_data.get('subject', '').strip()
            if not email_subject:
                email_subject = f"Claim Submission from {sender_name}"
            
            claim_data = {
                'workflow_id': workflow_id,
                'email_subject': email_subject,
                'email_body': email_data.get('body', '').strip(),
                'sender_email': sender_email,
                'sender_name': sender_name,
                'received_at': email_data.get('received_at', datetime.utcnow().isoformat()),
                
                # AI Classification Results
                'claim_type': final_analysis.get('claim_type', 'general'),
                'urgency_level': final_analysis.get('urgency_level', 'medium'),
                'confidence_level': final_analysis.get('confidence', 'medium'),
                'ai_classification_result': classification_result,
                'consensus_confidence': classification_result.get('consensus_confidence', 0.0),
                
                # Extracted claim details
                'policy_number': extracted_details.get('policy_number'),
                'incident_date': extracted_details.get('incident_date'),
                'incident_location': extracted_details.get('incident_location'),
                'estimated_value': extracted_details.get('estimated_value'),
                'claimant_name': extracted_details.get('claimant_name'),
                'claimant_phone': extracted_details.get('claimant_phone'),

                # Zendesk Integration (if provided)
                'zendesk_ticket_id': zendesk_ticket_id,
                'zendesk_ticket_url': zendesk_ticket_url,

                # Initial status
                'status': 'processing' if zendesk_ticket_id else 'received'
            }
            
            # Create claim record
            created_claim = await self.supabase.create_claim(claim_data)
            claim_id = created_claim['id']  # Database uses 'id' as primary key
            
            # Add initial history entry
            await self.supabase.add_claim_history(
                claim_id=claim_id,
                event_type="claim_created",
                description="Claim record created from AI-classified email",
                new_values=claim_data,
                metadata={
                    'workflow_id': workflow_id,
                    'ai_models_used': ['gpt-4o', 'gpt-4o-mini'],
                    'consensus_confidence': classification_result.get('consensus_confidence')
                }
            )

            return claim_id
            
        except Exception as e:
            dozzle_log("error", "❌ [CLAIMS_PROCESSOR] Failed to create claim record",
                      workflow_id=workflow_id,
                      error=str(e))
            raise
    
    async def _process_attachments(self,
                                 claim_id: str,
                                 workflow_id: str,
                                 attachments: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Process and store email attachments in Supabase Storage
        
        Args:
            claim_id: UUID of the claim
            workflow_id: Workflow ID for organization
            attachments: List of attachment data
            
        Returns:
            List of processed attachment records
        """
        processed_attachments = []
        
        try:
            dozzle_log("info", "📎 [CLAIMS_PROCESSOR] Processing attachments",
                      claim_id=claim_id,
                      attachment_count=len(attachments))
            
            for i, attachment in enumerate(attachments):
                try:
                    # Extract attachment information
                    filename = attachment.get('filename', f'attachment_{i+1}')
                    content_type = attachment.get('content_type', 'application/octet-stream')
                    file_content = attachment.get('content', b'')
                    
                    if not file_content:
                        dozzle_log("warning", "⚠️ [CLAIMS_PROCESSOR] Skipping empty attachment",
                                  filename=filename)
                        continue
                    
                    # Upload to Supabase Storage
                    attachment_record = await self.supabase.upload_attachment(
                        claim_id=claim_id,
                        workflow_id=workflow_id,
                        file_content=file_content,
                        filename=filename,
                        content_type=content_type
                    )
                    
                    processed_attachments.append(attachment_record)
                    
                    dozzle_log("info", "✅ [CLAIMS_PROCESSOR] Attachment processed",
                              attachment_id=attachment_record['attachment_id'],
                              filename=filename,
                              size=len(file_content))
                    
                except Exception as e:
                    dozzle_log("error", "❌ [CLAIMS_PROCESSOR] Failed to process attachment",
                              claim_id=claim_id,
                              filename=attachment.get('filename', 'unknown'),
                              error=str(e))
                    # Continue processing other attachments
                    continue
            
            # Add attachment processing to audit trail
            if processed_attachments:
                await self.supabase.add_claim_history(
                    claim_id=claim_id,
                    event_type="attachments_processed",
                    description=f"Processed {len(processed_attachments)} attachments",
                    new_values={
                        'attachments_count': len(processed_attachments),
                        'attachment_ids': [att['attachment_id'] for att in processed_attachments]
                    },
                    metadata={
                        'workflow_id': workflow_id,
                        'total_size': sum(att['file_size'] for att in processed_attachments)
                    }
                )
            
            dozzle_log("info", "✅ [CLAIMS_PROCESSOR] All attachments processed",
                      claim_id=claim_id,
                      processed_count=len(processed_attachments),
                      total_count=len(attachments))
            
            return processed_attachments
            
        except Exception as e:
            dozzle_log("error", "❌ [CLAIMS_PROCESSOR] Attachment processing failed",
                      claim_id=claim_id,
                      error=str(e))
            raise
    
    async def _create_zendesk_ticket(self,
                                   claim_id: Optional[str],
                                   workflow_id: str,
                                   email_data: Dict[str, Any],
                                   classification_result: Dict[str, Any],
                                   attachments: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Create professional Zendesk ticket with clean formatting for insurance claims

        Args:
            claim_id: UUID of the claim (can be None if creating ticket first)
            workflow_id: Workflow ID from email processing
            email_data: Original email information
            classification_result: AI analysis results
            attachments: List of processed attachments

        Returns:
            Zendesk ticket creation result
        """
        try:
            dozzle_log("info", "🎫 [CLAIMS_PROCESSOR] Creating professional Zendesk ticket",
                      claim_id=claim_id,
                      workflow_id=workflow_id)
            
            # Create professional ticket with improved formatting
            ticket_result = await self.zendesk.create_simple_ticket(
                claim_id=claim_id,
                workflow_id=workflow_id,
                email_data=email_data,
                attachments=attachments
            )
            
            dozzle_log("info", "✅ [CLAIMS_PROCESSOR] Professional Zendesk ticket created",
                      claim_id=claim_id,
                      ticket_id=ticket_result.get('id'),
                      priority=ticket_result.get('priority'))
            
            return ticket_result

        except Exception as e:
            dozzle_log("error", "❌ [CLAIMS_PROCESSOR] Professional Zendesk ticket creation failed",
                      claim_id=claim_id,
                      workflow_id=workflow_id,
                      error=str(e))
            raise

    async def _update_zendesk_ticket_with_attachments(self,
                                                    ticket_id: str,
                                                    processed_attachments: List[Dict[str, Any]]) -> None:
        """
        Update Zendesk ticket with professional attachment information

        Args:
            ticket_id: Zendesk ticket ID
            processed_attachments: List of processed attachment records
        """
        try:
            if not processed_attachments:
                return

            dozzle_log("info", "📎 [CLAIMS_PROCESSOR] Updating Zendesk ticket with professional attachment info",
                      ticket_id=ticket_id,
                      attachment_count=len(processed_attachments))

            # Create professional attachment summary
            attachment_comment = f"""📁 **ATTACHMENTS PROCESSED** ({len(processed_attachments)} files)

**Document Upload Summary:**"""

            total_size = 0
            for i, attachment in enumerate(processed_attachments, 1):
                file_size = attachment.get('file_size', 0)
                total_size += file_size
                
                attachment_comment += f"""
{i}. **{attachment['filename']}**
   • Size: {self._format_file_size(file_size)}
   • Type: {attachment.get('content_type', 'Unknown')}
   • Status: ✅ Uploaded Successfully"""
                
                if attachment.get('signed_url'):
                    attachment_comment += f"\n   • 📎 [Download Link]({attachment['signed_url']})"

            attachment_comment += f"""

---

**Upload Summary:**
• **Total Files:** {len(processed_attachments)}
• **Total Size:** {self._format_file_size(total_size)}
• **Status:** All attachments processed and stored securely
• **Next Steps:** Attachments ready for claims adjuster review"""

            # Add professional comment to ticket
            await self.zendesk.add_comment_to_ticket(
                ticket_id=ticket_id,
                comment=attachment_comment,
                public=False  # Internal comment
            )

            dozzle_log("info", "✅ [CLAIMS_PROCESSOR] Zendesk ticket updated with professional attachment info",
                      ticket_id=ticket_id,
                      attachment_count=len(processed_attachments),
                      total_size=self._format_file_size(total_size))

        except Exception as e:
            dozzle_log("error", "❌ [CLAIMS_PROCESSOR] Failed to update Zendesk ticket with attachments",
                      ticket_id=ticket_id,
                      error=str(e))
            # Don't raise - this is not critical to the main workflow

    def _format_file_size(self, size_bytes: int) -> str:
        """Format file size in human-readable format"""
        if size_bytes == 0:
            return "0 B"
        size_names = ["B", "KB", "MB", "GB"]
        i = 0
        while size_bytes >= 1024 and i < len(size_names) - 1:
            size_bytes /= 1024.0
            i += 1
        return f"{size_bytes:.1f} {size_names[i]}"
