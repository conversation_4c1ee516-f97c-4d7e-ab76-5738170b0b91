#!/usr/bin/env python3
"""
Test script to verify Zendesk ticket creation and attachment fixes
"""

import asyncio
import json
import os
from datetime import datetime
from src.config.settings import Settings
from src.database.supabase_client import SupabaseClient
from src.zendesk_integration.zendesk_client import ZendeskClient

async def test_zendesk_fixes():
    """Test the Zendesk fixes with sample data"""
    print("🧪 Testing Zendesk Integration Fixes...")
    
    # Initialize components
    settings = Settings()
    supabase = SupabaseClient(settings)
    zendesk = ZendeskClient(settings, supabase)
    
    # Sample email data
    email_data = {
        'subject': 'Claim Submission 57',
        'body': '''Dear Zurich Claims Team,

I am writing to formally submit the documents related to the injury I sustained on August 27, 2020, due to a slip-and-fall incident at the [No Frills location] while I was approaching the checkout area.

As discussed in earlier communications, I slipped on what appeared to be water on the floor, resulting in injuries that required medical attention. I am seeking support for the associated medical costs and any applicable compensation under the store's liability insurance.

Please find the following documents attached for your review:

• A scanned copy of the incident report
• My medical records and treatment summary
• A signed medical authorization form
• A copy of my government-issued ID

I kindly request that you review my claim and let me know if any further information or documentation is needed. I would appreciate an update regarding the coverage assessment and claim status at your earliest convenience.

Thank you for your time and assistance.

Sincerely,
Jacques E.''',
        'sender_email': '<EMAIL>',
        'sender_name': 'Jacques E.',
        'received_at': datetime.now().isoformat()
    }
    
    # Sample classification result
    classification_result = {
        'final_analysis': {
            'email_type': 'CLAIM_SUBMISSION',
            'claim_type': 'PERSONAL_INJURY',
            'urgency_level': 'HIGH',
            'confidence_level': 'VERY_HIGH',
            'extracted_details': {
                'incident_date': '2020-08-27',
                'incident_location': 'No Frills (slip-and-fall)',
                'claimant_name': 'Jacques E.',
                'policy_number': None,
                'estimated_value': None
            }
        },
        'consensus_confidence': 0.92
    }
    
    # Sample attachments (simulated)
    sample_attachments = [
        {
            'filename': 'incident_report.pdf',
            'content': b'PDF content here...',  # Simulated binary content
            'content_type': 'application/pdf',
            'file_size': 1024
        },
        {
            'filename': 'medical_records.pdf',
            'content': b'Medical records PDF content...',
            'content_type': 'application/pdf', 
            'file_size': 2048
        }
    ]
    
    try:
        print("📧 Creating sample ticket with AI analysis...")
        
        # Create ticket (this should now work without duplicates)
        ticket_result = await zendesk.create_simple_ticket(
            claim_id="test-claim-id-123",
            workflow_id="test-workflow-456",
            email_data=email_data,
            attachments=sample_attachments  # This should upload properly
        )
        
        print(f"✅ Ticket created successfully!")
        print(f"   📝 Ticket ID: {ticket_result.get('id')}")
        print(f"   🔗 Ticket URL: {ticket_result.get('url')}")
        print(f"   📎 Attachments: {len(sample_attachments)} files")
        
        # Add AI analysis comment
        print("🤖 Adding AI analysis comment...")
        ai_comment_result = await zendesk.add_ai_analysis_comment(
            ticket_id=str(ticket_result.get('id')),
            classification_result=classification_result,
            claim_id="TEST-CLAIM-123"
        )
        
        if ai_comment_result:
            print("✅ AI analysis comment added successfully!")
        else:
            print("❌ Failed to add AI analysis comment")
            
        print("\n🎉 All tests completed successfully!")
        print("The fixes should resolve:")
        print("  ✅ Duplicate content in first comment")
        print("  ✅ File attachments not uploading")
        print("  ✅ Missing AI analysis in second comment")
        
        return ticket_result
        
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        raise

if __name__ == "__main__":
    asyncio.run(test_zendesk_fixes()) 