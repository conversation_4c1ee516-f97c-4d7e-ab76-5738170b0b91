#!/usr/bin/env python3
"""
Test Enhanced Email Threading System
Tests the 2025 research-based email threading implementation with:
- mail-parser-reply: Multi-language reply parsing
- email.utils: Professional Message-ID and threading headers
- Conversation tracking and thread management
"""

import os
import sys
import json
from datetime import datetime

# Add the src directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from communications.email_threading import EmailThreadingManager, threading_manager

def test_basic_functionality():
    """Test basic email threading functionality"""
    print("🧪 Testing Basic Email Threading Functionality")
    print("=" * 50)
    
    # Create a new threading manager for testing
    tm = EmailThreadingManager(domain="test.zurich.com")
    
    # Test Message-ID generation
    print("\n1. Testing Message-ID Generation:")
    basic_id = tm.generate_message_id()
    claim_id = tm.generate_message_id(claim_id="PI123456")
    
    print(f"   Basic Message-ID: {basic_id}")
    print(f"   Claim Message-ID: {claim_id}")
    
    # Test email address parsing
    print("\n2. Testing Email Address Parsing:")
    test_addresses = [
        "<PERSON> <<EMAIL>>",
        "<EMAIL>",
        "<PERSON> <PERSON> <<EMAIL>>"
    ]
    
    for addr in test_addresses:
        name, email = tm.extract_email_components(addr)
        formatted = tm.format_email_address(name, email)
        print(f"   Original: {addr}")
        print(f"   Parsed: Name='{name}', Email='{email}'")
        print(f"   Formatted: {formatted}")
        print()
    
    return tm

def test_reply_parsing():
    """Test advanced reply content parsing"""
    print("\n🧪 Testing Advanced Reply Content Parsing")
    print("=" * 50)
    
    tm = EmailThreadingManager()
    
    # Test emails with various threading scenarios
    test_emails = [
        {
            "name": "Simple New Email",
            "content": "Hello, I need to file a personal injury claim from yesterday's incident."
        },
        {
            "name": "Reply with Quoted Content",
            "content": """Thank you for your response. I have additional information to add.

On Mon, Jan 15, 2024 at 2:30 PM, Claims Support <<EMAIL>> wrote:
> Thank you for contacting Zurich Insurance. We have received your claim
> and will process it within 3-5 business days.
> 
> Best regards,
> Claims Department"""
        },
        {
            "name": "Multi-level Reply Chain",
            "content": """I have attached the medical reports as requested.

On Wed, Jan 17, 2024 at 10:15 AM, Claims Agent <<EMAIL>> wrote:
> We need the medical documentation to proceed with your claim.
> 
> On Mon, Jan 15, 2024 at 2:30 PM, Claims Support <<EMAIL>> wrote:
> > Thank you for contacting Zurich Insurance. We have received your claim
> > and will process it within 3-5 business days."""
        },
        {
            "name": "German Reply (Multi-language)",
            "content": """Vielen Dank für Ihre Antwort. Ich habe zusätzliche Informationen.

Am Mo., 15. Jan. 2024 um 14:30 schrieb Claims Support <<EMAIL>>:
> Vielen Dank für Ihre Kontaktaufnahme mit Zurich Insurance.
> Wir haben Ihren Anspruch erhalten und werden ihn innerhalb von 3-5 Werktagen bearbeiten.
> 
> Mit freundlichen Grüßen,
> Schadensteam"""
        }
    ]
    
    for test_email in test_emails:
        print(f"\n--- Testing: {test_email['name']} ---")
        result = tm.extract_clean_reply(test_email['content'])
        
        print(f"Parsing Method: {result['parsing_method']}")
        print(f"Reply Chain Length: {result['reply_chain_length']}")
        print(f"Has Previous Messages: {result['has_previous_messages']}")
        
        clean_content = result['clean_content'][:200] + "..." if len(result['clean_content']) > 200 else result['clean_content']
        print(f"Clean Content: {clean_content}")
        
        if 'signatures' in result:
            print(f"Signatures Found: {len(result['signatures'])}")
        if 'headers' in result:
            print(f"Headers Found: {len(result['headers'])}")
        
        print()

def test_threading_headers():
    """Test email threading header generation"""
    print("\n🧪 Testing Email Threading Headers")
    print("=" * 50)
    
    tm = EmailThreadingManager()
    
    # Simulate a conversation thread
    thread_messages = []
    
    # Initial message
    msg1_id = tm.generate_message_id(claim_id="PI789012")
    headers1 = tm.create_thread_headers(msg1_id)
    thread_messages.append(msg1_id)
    
    print("1. Initial Message Headers:")
    for key, value in headers1.items():
        print(f"   {key}: {value}")
    
    # First reply
    msg2_id = tm.generate_message_id(claim_id="PI789012")
    headers2 = tm.create_thread_headers(
        message_id=msg2_id,
        in_reply_to=msg1_id,
        references=[msg1_id]
    )
    thread_messages.append(msg2_id)
    
    print("\n2. First Reply Headers:")
    for key, value in headers2.items():
        print(f"   {key}: {value}")
    
    # Second reply
    msg3_id = tm.generate_message_id(claim_id="PI789012")
    headers3 = tm.create_thread_headers(
        message_id=msg3_id,
        in_reply_to=msg2_id,
        references=thread_messages
    )
    
    print("\n3. Second Reply Headers:")
    for key, value in headers3.items():
        print(f"   {key}: {value}")

def test_conversation_tracking():
    """Test conversation thread tracking"""
    print("\n🧪 Testing Conversation Thread Tracking")
    print("=" * 50)
    
    tm = EmailThreadingManager()
    
    # Create a claim thread
    claim_id = "PI555666"
    thread_id = tm.generate_claim_thread_id(claim_id)
    
    print(f"Thread ID: {thread_id}")
    
    # Add messages to the thread
    messages = [
        {
            'subject': 'New Personal Injury Claim',
            'from': '<EMAIL>',
            'clean_content': 'I need to file a personal injury claim for an incident yesterday.',
            'attachments': ['incident_photo.jpg']
        },
        {
            'subject': 'Re: New Personal Injury Claim - Additional Documentation Required',
            'from': '<EMAIL>',
            'clean_content': 'Thank you for your claim. We need medical documentation to proceed.',
            'attachments': []
        },
        {
            'subject': 'Re: New Personal Injury Claim - Medical Reports Attached',
            'from': '<EMAIL>',
            'clean_content': 'Please find the medical reports attached.',
            'attachments': ['medical_report.pdf', 'doctor_note.pdf']
        }
    ]
    
    for i, msg_data in enumerate(messages, 1):
        msg_id = tm.generate_message_id(claim_id=claim_id)
        tm.track_conversation(msg_id, thread_id, msg_data)
        print(f"   Added message {i}: {msg_data['subject'][:50]}...")
    
    # Get conversation summary
    print("\nConversation Summary:")
    summary = tm.get_conversation_summary(thread_id)
    print(json.dumps(summary, indent=2, default=str))

def test_threading_statistics():
    """Test threading system statistics"""
    print("\n🧪 Testing Threading System Statistics")
    print("=" * 50)
    
    stats = threading_manager.get_threading_stats()
    
    print("Threading System Statistics:")
    print(json.dumps(stats, indent=2, default=str))

def test_multilingual_support():
    """Test multi-language support"""
    print("\n🧪 Testing Multi-Language Support")
    print("=" * 50)
    
    tm = EmailThreadingManager()
    
    multilingual_tests = [
        {
            "language": "German",
            "content": """Ich benötige Hilfe mit meinem Schadensfall.

Am Mo., 15. Jan. 2024 um 14:30 schrieb Support <<EMAIL>>:
> Vielen Dank für Ihre Nachricht. Wir werden Ihren Fall bearbeiten.
> 
> Mit freundlichen Grüßen,
> Support Team"""
        },
        {
            "language": "French", 
            "content": """Je vous remercie pour votre réponse rapide.

Le lun. 15 janv. 2024 à 14:30, Support <<EMAIL>> a écrit :
> Merci de nous avoir contactés. Nous examinerons votre réclamation.
> 
> Cordialement,
> Équipe de support"""
        },
        {
            "language": "Spanish",
            "content": """Gracias por su respuesta. Tengo información adicional.

El lun, 15 ene 2024 a las 14:30, Soporte <<EMAIL>> escribió:
> Gracias por contactarnos. Procesaremos su reclamo pronto.
> 
> Saludos cordiales,
> Equipo de Soporte"""
        }
    ]
    
    for test in multilingual_tests:
        print(f"\n--- Testing {test['language']} ---")
        result = tm.extract_clean_reply(test['content'])
        
        print(f"Parsing Method: {result['parsing_method']}")
        print(f"Content Length: {len(result['clean_content'])} chars")
        clean_preview = result['clean_content'][:100] + "..." if len(result['clean_content']) > 100 else result['clean_content']
        print(f"Clean Content Preview: {clean_preview}")

def main():
    """Run all email threading tests"""
    print("🚀 Enhanced Email Threading System Tests")
    print("Based on 2025 research: mail-parser-reply + email.utils")
    print("=" * 60)
    
    try:
        # Run all tests
        tm = test_basic_functionality()
        test_reply_parsing()
        test_threading_headers()
        test_conversation_tracking()
        test_threading_statistics()
        test_multilingual_support()
        
        print("\n✅ All Enhanced Email Threading Tests Completed Successfully!")
        print("\nKey Features Demonstrated:")
        print("- ✉️  RFC-compliant Message-ID generation")
        print("- 🧵 Professional email threading headers")
        print("- 🔄 Advanced reply content extraction")
        print("- 🌍 Multi-language support (8 languages)")
        print("- 📊 Conversation tracking and analytics")
        print("- 🔧 Fallback mechanisms for reliability")
        
        # Show final system status
        print(f"\nSystem Status:")
        stats = threading_manager.get_threading_stats()
        print(f"- Enhanced Parsing Available: {stats['enhanced_parsing_available']}")
        print(f"- Supported Languages: {len(stats['supported_languages'])}")
        print(f"- Total Threads Tracked: {stats['total_conversation_threads']}")
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main() 